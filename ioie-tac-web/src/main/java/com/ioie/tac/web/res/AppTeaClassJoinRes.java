package com.ioie.tac.web.res;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class AppTeaClassJoinRes {
    /**
     * 班课id
     */
    private Long id;
    /**
     * 课程id
     */
    private Long courseId;
    /**
     * 班级名称
     */
    private String className;
    /**
     * 班课名称
     */
    private String courseName;
    /**
     * 班课创建者
     */
    private String creator;
    /**
     * 班课码
     */
    private String classCode;
    /**
     * 班课卡片背景图
     */
    private String imageUrl;
    /**
     * 加入类型：班课身份
     */
    private Integer joinType;
    /**
     * 班课类型
     */
    private Integer type;
    /**
     * 班课是否结束标识
     */
    private Integer activeFlag;
    /**
     * 是否允许进入班课
     */
    private Integer enterFlag;
    /**
     * 楼层分类
     */
    private String floor;
    /**
     * 是否机构班课
     */
    private Integer gokFlag;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 租户名称
     */
    private String  tenantName;
    /**
     * 返回信息
     */
    private String  message;
}
