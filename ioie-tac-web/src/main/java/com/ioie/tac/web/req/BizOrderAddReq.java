package com.ioie.tac.web.req;

import com.ioie.web.validation.annotation.LongVerify;
import com.ioie.web.validation.annotation.StringVerify;

import lombok.Data;

/**
 * description: 订单提交参数
 *
 * <AUTHOR> jy.chen
 * @version : 1.0
 * @since : 2022-02-23
 */
@Data
public class BizOrderAddReq {

    /**
     * 课程id
     */
    @LongVerify(name = "课程id", required = true)
    private Long courseId;

    /**
     * 成长路径id
     */
    @LongVerify(name = "成长路径id",required = true)
    private Long growthId;

    /**
     * 报名来源：首页-HOME_PAGE；GROWTH_PATH-选择成长路径页面
     */
    @StringVerify(name = "报名来源", regexp = "^(GROWTH_PATH|HOME_PAGE|QR_CODE)$", required = true)
    private String registrationSource;

}
