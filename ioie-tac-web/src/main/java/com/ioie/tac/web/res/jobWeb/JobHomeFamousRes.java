package com.ioie.tac.web.res.jobWeb;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * description: 就业首页 名企热招
 *
 * <AUTHOR> lld
 * @version : 1.0
 * @slogan : Let life be beautiful like summer flowers and death like autumn leaves.
 * @since : 2022-07-07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class JobHomeFamousRes {

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 企业id
     */
    private Long companyId;

    /**
     * 企业头像
     */
    private String companyAvatar;

    /**
     * 行业类型
     */
    private String industryType;

    /**
     * 行业类型id, 字典值
     */
    private Integer industryTypeId;

    /**
     * 热招职位名称
     */
    private String positionName;

    /**
     * 热招职位id
     */
    private Long positionId;

    /**
     * 热招职位数量
     */
    private Integer positionNum;

    /**
     * 薪资
     */
    private String salary;




}
