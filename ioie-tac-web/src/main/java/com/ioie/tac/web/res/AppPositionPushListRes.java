package com.ioie.tac.web.res;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppPositionPushListRes {

    /**
     * 岗位id
     */
    private Long positionId;

    /**
     * 岗位名称
     */
    private String positionName;

    /**
     * 薪资范围等级：0-面议；1-月薪 2-日薪
     */
    private String salary;

    /**
     * 职位类型
     */
    private String positionType;

    /**
     * 最低学历要求（数据字典）
     */
    private String eduReq;

    /**
     * 工作经验(数据字典)
     */
    private String workExp;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 公司logo
     */
    private String logo;

    /**
     * 公司地点
     */
    private String location;

    /**
     * 公司行业类型
     */
    private String industryType;

    /**
     * 职位上架状态
     */
    private Integer validFlag;

    /**
     * 职位是否被删除
     */
    private Integer dataValid;

    /**
     * 邀请时间
     */
    private Long pushTime;

    /**
     * 招聘人数
     */
    private Integer inviteNums;
}
