package com.ioie.tac.web.service;

import com.ioie.common.res.BasePageRes;
import com.ioie.common.res.OpRes;
import com.ioie.common.resolver.AppVersion;
import com.ioie.tac.web.constant.enums.AppVersionSortRule;
import com.ioie.tac.web.req.AppLowestVersionReq;
import com.ioie.tac.web.req.AppVersionReq;
import com.ioie.tac.web.res.AppVersionOpRes;
import com.ioie.tac.web.res.AppVersionRes;

/**
 * description: app版本控制业务接口
 *
 * <AUTHOR> jy.chen
 * @version : 1.0
 * @since : 2021-08-03
 */
public interface AppVersionService {

    /**
     * 获取最新的版本记录
     *
     * @param versionNum 版本号
     * @param appVersion {@link AppVersion}
     * @return {@link AppVersionRes}
     */
    AppVersionRes getLatestAppVersion(Integer versionNum, AppVersion appVersion);

    /**
     * 版本管理列表
     *
     * @param platform 平台标识
     * @param versionName 版本名称
     * @param isLatestVersion 最新版本标识
     * @param isLowestVersion 最新版本标识
     * @param isForceUpdate 强制更新标识
     * @param sortRule {@link AppVersionSortRule}
     * @param pageNo 页码
     * @param pageSize 偏移量
     * @return {@link BasePageRes}
     */
    BasePageRes<AppVersionOpRes> listAppVersions(String platform, String versionName, String isLatestVersion, String isLowestVersion,
                                                 String isForceUpdate, String sortRule, int pageNo, int pageSize);

    /**
     * 版本详情
     *
     * @param id id
     * @return {@link AppVersionOpRes}
     */
    AppVersionOpRes getAppVersion(Long id);

    /**
     * 创建版本
     *
     * @param req {@link AppVersionReq}
     * @return {@link OpRes}
     */
    OpRes saveAppVersion(AppVersionReq req);

    /**
     * 编辑版本
     *
     * @param id id
     * @param req {@link AppVersionReq}
     * @return {@link OpRes}
     */
    OpRes editAppVersion(Long id, AppVersionReq req);

    /**
     * 删除版本信息
     *
     * @param id id
     * @return {@link OpRes}
     */
    OpRes deleteAppVersion(Long id);

    /**
     * 更新该版本为最低版本，即低于该版本都强制更新
     *
     * @param id 版本id
     * @param req {@link AppLowestVersionReq}
     * @return {@link OpRes}
     */
    OpRes editLowestAppVersion(Long id, AppLowestVersionReq req);
}
