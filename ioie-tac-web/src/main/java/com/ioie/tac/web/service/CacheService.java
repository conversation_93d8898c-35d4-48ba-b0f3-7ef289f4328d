package com.ioie.tac.web.service;

import java.util.List;
import java.util.Map;

import com.ioie.tac.web.constant.enums.CacheReportType;
import com.ioie.tac.web.model.RecentlyWatchRecord;

/**
 * description: 业务缓存接口
 *
 * <AUTHOR> jy.chen
 * @version : 1.0
 * @since : 2021-10-08
 */
public interface CacheService {


    /**
     * 获取学生最近查看一条历史记录
     * 
     * @param classId   班课id
     * @param memberId  成员id
     * @return {@link RecentlyWatchRecord}
     */
    RecentlyWatchRecord getRecentlyWatchRecord(Long classId, Long memberId);

    /**
     * 获取指定业务ids对应的名称Map
     *
     * @param cacheType {@link CacheReportType}
     * @param ids 主键ids
     * @return 名称
     */
    Map<Long, String> getNameMapByList(CacheReportType cacheType, List<Long> ids);


    /**
     * 获取指定业务ids对应的图片Map
     *
     * @param cacheType {@link CacheReportType}
     * @param ids 主键ids
     * @return 名称
     */
    Map<Long, String> getImgMapByList(CacheReportType cacheType, List<Long> ids);

}
