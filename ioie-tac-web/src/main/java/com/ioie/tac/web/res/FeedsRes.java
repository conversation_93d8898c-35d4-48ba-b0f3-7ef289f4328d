package com.ioie.tac.web.res;

import com.ioie.tac.db.res.AppSelfOfflineActivityRes;
import com.ioie.tac.integration.JobVO;
import com.ioie.tac.web.res.app.AppIndexPositionRes;
import com.ioie.tac.web.res.app.AppProjectRes;
import com.ioie.tac.web.res.app.AppRecommendedCourseRes;
import com.ioie.tac.web.res.appmng.AppMngNewsRes;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 8/24/2021
 */
@Data
public class FeedsRes {
    /**
     * 数据类型  1资讯 2公开课 3就业(预留) 4线下活动 5热门课程    6、项目分发
     */
    private int    type;
    private Object data;

    public static FeedsRes from(AppMngNewsRes appMngNewsRes) {
        final FeedsRes feedsRes = new FeedsRes();
        feedsRes.setData(appMngNewsRes);
        feedsRes.setType(1);
        return feedsRes;
    }

    public static FeedsRes from(PublicClassRes publicClassRes) {
        final FeedsRes feedsRes = new FeedsRes();
        feedsRes.setType(2);
        feedsRes.setData(publicClassRes);
        return feedsRes;
    }

    public static FeedsRes from(AppIndexPositionRes o) {
        final FeedsRes feedsRes = new FeedsRes();
        feedsRes.setData(o);
        feedsRes.setType(3);
        return feedsRes;
    }

    public static FeedsRes from(AppSelfOfflineActivityRes o) {
        final FeedsRes feedsRes = new FeedsRes();
        feedsRes.setData(o);
        feedsRes.setType(4);
        return feedsRes;
    }

    public static FeedsRes from(List<AppRecommendedCourseRes> o) {
        final FeedsRes feedsRes = new FeedsRes();
        feedsRes.setData(o);
        feedsRes.setType(5);
        return feedsRes;
    }

    public static FeedsRes from(AppProjectRes o) {
        final FeedsRes feedsRes = new FeedsRes();
        feedsRes.setData(o);
        feedsRes.setType(6);
        return feedsRes;
    }
}
