package com.ioie.tac.web.controller;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import com.ioie.tac.db.dao.PartnerDemandPoolDao;
import com.ioie.tac.web.security.annotation.PortalChannel;
import com.ioie.tac.web.service.PartnerCourseService;
import com.ioie.tac.web.service.PartnerDemandPoolService;
import com.ioie.tac.web.service.PartnerProjectService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.ioie.common.res.BasePageRes;
import com.ioie.common.res.OpRes;
import com.ioie.tac.web.res.PortalDemandPoolAddRes;
import com.ioie.tac.web.res.TeachingPortalCourseRes;
import com.ioie.tac.web.res.TeachingPortalProjectInfoRes;
import com.ioie.tac.web.res.TeachingPortalProjectRes;

/**
 * description: 基地门户 - 合作（项目、课程、需求池）
 *
 * @menu 教学端 -> 合作（项目、课程、需求池）
 *
 * <AUTHOR> lld
 * @version : 1.0
 * @slogan : Let life be beautiful like summer flowers and death like autumn leaves.
 * @since : 2022-09-07
 */
@RestController
@RequestMapping("/portal-partner")
@Validated
@RequiredArgsConstructor
public class PortalPartnerController {

    /**
     * 合作需求池
     */
    private final PartnerDemandPoolService partnerDemandPoolService;

    /**
     * 合作项目
     */
    private final PartnerProjectService partnerProjectService;

    /**
     * 合作课程
     */
    private final PartnerCourseService partnerCourseService;

    /**
     * v260 教学端 -> 我要合作
     * @param portalDemandPoolAddRes 需求池新增
     * @return {@link OpRes}
     */
    @PostMapping("demand-pool")
    @PortalChannel
    public OpRes addDemandPool(@RequestBody @Valid PortalDemandPoolAddRes portalDemandPoolAddRes) {
        return partnerDemandPoolService.addDemandPool(portalDemandPoolAddRes);
    }


    /**
     * v260 教学端 -> 合作项目展示列表
     * @param categoryId 全部:传0，合作项目分类id 字典value 值 对应的字典code PROJECT_CATEGORY
     * @return {@link TeachingPortalProjectRes}
     */
    @GetMapping("project")
    @PortalChannel
    public BasePageRes<TeachingPortalProjectRes> portalProjectList(HttpServletRequest request, @Valid @RequestParam(value = "categoryId") Integer categoryId) {
        return partnerProjectService.portalProjectList(request, categoryId);
    }

    /**
     * v260 教学端 -> 合作项目详情
     * @param projectId 项目id
     * @return {@link TeachingPortalProjectInfoRes}
     */
    @GetMapping("project/{projectId}/info")
    @PortalChannel
    public TeachingPortalProjectInfoRes portalProjectInfo(@PathVariable Long projectId) {
        return partnerProjectService.portalProjectInfo(projectId);
    }


    /**
     * v260 教学端 -> 合作课程展示列表
     * @return {@link TeachingPortalCourseRes}
     */
    @GetMapping("course")
    @PortalChannel
    public BasePageRes<TeachingPortalCourseRes> portalCourseList(HttpServletRequest request) {
        return partnerCourseService.portalCourseList(request);
    }






}
