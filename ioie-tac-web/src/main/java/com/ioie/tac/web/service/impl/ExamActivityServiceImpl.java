package com.ioie.tac.web.service.impl;

import static com.ioie.tac.web.constant.error.ExamActivityError.EXAM_PROCESS_ERROR;
import static com.ioie.tac.web.constant.error.ExamActivityError.NO_CORRECTED;
import static com.ioie.tac.web.service.impl.ExamActivityStudentServiceImpl.getExamTopicIds;
import static com.ioie.tac.web.service.impl.ExamActivityStudentServiceImpl.getTopicOptionList;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.annotation.Resource;

import com.ioie.tac.db.dto.ExamStudentDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.boot.autoconfigure.klock.model.LockTimeoutStrategy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.AtomicDouble;
import com.ioie.common.constant.CmmConstants;
import com.ioie.common.enums.ApprovalEnum;
import com.ioie.common.enums.DataValidEnum;
import com.ioie.common.enums.YesNoEnum;
import com.ioie.common.enums.error.BaseCmmError;
import com.ioie.common.exception.BizException;
import com.ioie.common.holder.TenantMemberHolder;
import com.ioie.common.holder.UserInfoHolder;
import com.ioie.common.req.IdsReq;
import com.ioie.common.res.BasePageRes;
import com.ioie.common.res.BaseRes;
import com.ioie.common.res.OpRes;
import com.ioie.common.resolver.TenantMemberInfo;
import com.ioie.common.utils.DateHelper;
import com.ioie.common.utils.RandomUtils;
import com.ioie.common.utils.ReflectionUtils;
import com.ioie.common.utils.SnowflakeIdWorker;
import com.ioie.context.utils.ContextHelper;
import com.ioie.db.resmap.CountResmap;
import com.ioie.kb.ms.provider.constants.enums.CalculateStuScoreDataTypeEnum;
import com.ioie.kb.ms.provider.req.*;
import com.ioie.kb.ms.provider.res.*;
import com.ioie.poi.utils.ExcelUtils;
import com.ioie.poi.utils.SheetExcelExportStyler;
import com.ioie.redis.lock.RedisDistributedLock;
import com.ioie.redis.template.RedisRepository;
import com.ioie.tac.db.dao.*;
import com.ioie.tac.db.entity.*;
import com.ioie.tac.integration.*;
import com.ioie.tac.web.constant.Constants;
import com.ioie.tac.web.constant.enums.*;
import com.ioie.tac.web.constant.error.ClassError;
import com.ioie.tac.web.constant.error.ExamActivityError;
import com.ioie.tac.web.consumer.res.CalculateGrowthMsg;
import com.ioie.tac.web.req.*;
import com.ioie.tac.web.res.*;
import com.ioie.tac.web.res.teaching.ExamCheckListV1Res;
import com.ioie.tac.web.res.teaching.TeaRecentExamRes;
import com.ioie.tac.web.service.ExamActivityService;
import com.ioie.tac.web.service.ExamMemberRoleService;
import com.ioie.tac.web.util.GetuiAsync;
import com.ioie.tac.web.util.SendCalculateMsgUtil;
import com.ioie.uc.ms.provider.res.PermissionTenantInfoRes;
import com.ioie.uc.ms.provider.res.TenantMemberPvdRes;
import com.ioie.uc.ms.provider.res.TenantRes;
import com.ioie.web.constant.CommonError;
import com.ioie.web.utils.BeanHelper;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import lombok.SneakyThrows;

/**
 * <AUTHOR>
 * @version v1.0.0
 * @Description: 考试活动
 * @see com.ioie.tac.web.service.impl
 * @since 2020-11-27 16:48:11
 */
@Service
public class ExamActivityServiceImpl extends ServiceImpl<ExamActivityDao, ExamActivityEntity> implements ExamActivityService {


    private static final String KAO_SHI = "KS";
    private static final int SIX_LENGTH = 6;

    @Resource
    private ExamActivityDao examActivityDao;

    @Resource
    private ExamActivityStudentDao examActivityStudentDao;

    @Resource
    private ExamStudentAnswerDao examStudentAnswerDao;

    @Resource
    private ExamStudentOptionDao examStudentOptionDao;

    @Resource
    private ExamMemberRoleDao examMemberRoleDao;

    @Resource
    private QuizPaperIntegration quizPaperIntegration;

    @Resource
    private TenantMemberIntegration tenantMemberIntegration;

    @Resource
    private ExamTopicDao examTopicDao;

    @Resource
    private ExamTopicOptionDao examTopicOptionDao;

    @Resource
    private RedisRepository redisRepository;

    @Resource
    private ExamMemberRoleService examMemberRoleService;

    @Resource
    private PartitionIntegration partitionIntegration;

    @Resource
    private PermissionGroupDao permissionGroupDao;


    @Resource
    private SourcePermissionDao sourcePermissionDao;

    @Resource
    private TopicLibraryIntegration topicLibraryIntegration;

    @Resource
    private TopicKnowledgeIntegration topicKnowledgeIntegration;

    @Resource
    private PermissionGroupTopDao permissionGroupTopDao;

    @Resource
    private TopicImportTargetIntegration topicImportTargetIntegration;

    @Resource
    private TopicExamTargetDao topicExamTargetDao;

    @Resource
    private GetuiAsync getuiAsync;

    @Resource
    private SendCalculateMsgUtil sendCalculateMsgUtil;

    @Resource
    private StuRecordRemarkDao stuRecordRemarkDao;

    @Resource
    private AppMessageDao appMessageDao;

    @Resource
    private TenantIntegration tenantIntegration;

    @Resource
    private TenantMemberIntegration memberIntegration;

    /**
     * 老师 创建考试（考卷库导入、资料库发起）
     *
     * @param paperId
     * @return
     * <AUTHOR>
     * @date 2020-11-30 14:50:11
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseRes send(Long paperId, ExamActivityReq examActivityReq) {
        //查询考卷
        QuizPaperPvdRes quizPaperPvdRes = quizPaperIntegration.paperById(paperId);
        //进行创建考试 录入题目
        ExamActivityEntity examActivityEntity = ExamActivityEntity.builder()
                .name(examActivityReq.getName())
                .introduction(examActivityReq.getIntroduction())
                .build();
        examActivityEntity.setOriPaperId(paperId);
        examActivityEntity.setOriPartitionId(quizPaperPvdRes.getPartitionId());
        examActivityEntity.setExamNo(KAO_SHI + RandomUtils.genByLen(SIX_LENGTH).toUpperCase());
        examActivityEntity.setExamNoStatus(YesNoEnum.YES.getValue());
        examActivityDao.insert(examActivityEntity);
        //选项列表
        List<ExamTopicOptionEntity> options = new ArrayList<>();
        List<TopicKnowledgePvdReq> topicKnowledgePvdReqList = new ArrayList<>();
        List<ExamTopicEntity> topics = quizPaperPvdRes.getTopics().stream().map(e -> {
            Long topicId = ContextHelper.context().getBean(SnowflakeIdWorker.class).nextId();
            List<TopicKnowledgePvdRes> knowledgeList = e.getKnowledgeIds();
            if(CollectionUtils.isNotEmpty(knowledgeList)) {
                topicKnowledgePvdReqList.addAll(knowledgeList.stream()
                        .map(t -> TopicKnowledgePvdReq.builder()
                                .topicId(topicId)
                                .knowledgeId(t.getId())
                                .type(KnowledgeTypeEnum.EXAM.getCode())
                                .build()
                        )
                        .collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(e.getOptions())) {
                List<ExamTopicOptionEntity> topicOptions = e.getOptions().stream().map(x -> ExamTopicOptionEntity.builder()
                        .topicId(topicId)
                        .option(x.getOption())
                        .sort(x.getSort())
                        .flag(x.getFlag())
                        .build()).collect(Collectors.toList());
                options.addAll(topicOptions);
            }
            ExamTopicEntity examTopicEntity = ExamTopicEntity.builder()
                    .examId(examActivityEntity.getId())
                    .type(e.getType())
                    .score(e.getScore())
                    .answer(e.getAnswer())
                    .analysis(e.getAnalysis())
                    .oriTopicId(e.getOriTopicId())
                    .createType(e.getCreateType())
                    .level(e.getLevel())
                    .sort(e.getSort())
                    .name(e.getName())
                    // v220 增加难度评级
                    .complexity(e.getComplexity())
                    .build();
            examTopicEntity.setId(topicId);
            return examTopicEntity;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(topics)) {
            examTopicDao.batchInsert(topics);
            if(CollectionUtils.isNotEmpty(topicKnowledgePvdReqList)){
                topicKnowledgeIntegration.addBatchTopicKnowledge(topicKnowledgePvdReqList);
            }
        }
        if (CollectionUtils.isNotEmpty(options)) {
            examTopicOptionDao.batchInsert(options);
        }
        BaseRes baseRes = new BaseRes();
        baseRes.setId(examActivityEntity.getId());
        return baseRes;
    }


    /**
     * @param examActivityReq
     * @Description: 老师-创建考试（考卷库导入、资料库发起）
     * <AUTHOR>
     * @date 2020-12-01 10:45:11
     * @return{@link BaseRes}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseRes add(ExamActivityReq examActivityReq) {
        ExamActivityEntity examActivityEntity = ExamActivityEntity.builder()
                .examNo(KAO_SHI + RandomUtils.genByLen(SIX_LENGTH).toUpperCase())
                .examNoStatus(YesNoEnum.YES.getValue())
                .name(examActivityReq.getName())
                .introduction(examActivityReq.getIntroduction())
                .build();
        //考试时长设置默认值 120分钟
        examActivityEntity.setTimeLimit(120);
        examActivityDao.insert(examActivityEntity);
        BaseRes baseRes = new BaseRes();
        baseRes.setId(examActivityEntity.getId());
        return baseRes;
    }

    /**
     * @param id
     * @Description: 老师-查看考试信息
     * <AUTHOR>
     * @date 2020-12-01 11:47:11
     * @return{@link ExamActivityInfoRes}
     */
    @Override
    public ExamActivityInfoRes info(Long id) {
        ExamActivityEntity examActivityEntity = examActivityDao.selectById(id);
        return ExamActivityInfoRes.builder()
                .id(examActivityEntity.getId())
                .name(examActivityEntity.getName())
                .introduction(examActivityEntity.getIntroduction())
                .build();
    }

    /**
     * @param id
     * @Description: 老师-修改考试信息
     * <AUTHOR>
     * @date 2020-12-01 11:47:11
     * @return{@link ExamActivityInfoRes}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(Long id, ExamActivityReq examActivityReq) {
        //判定权限
        examMemberRoleService.checkPermission(id, ExamMemberRoleEnum.AUTHOR.getValue(), ExamModuleEnum.BASE_INFO.getValue());
        ExamActivityEntity examActivityEntity = examActivityDao.selectById(id);
        examActivityEntity.setName(examActivityReq.getName());
        examActivityEntity.setIntroduction(examActivityReq.getIntroduction());
        examActivityDao.updateById(examActivityEntity);
    }


    /**
     * @param pageNo
     * @param pageSize
     * @Description: 老师-考试列表
     * <AUTHOR>
     * @date 2020-12-01 14:47:11
     * @return{@link ExamActivityInfoRes}
     */
    @Override
    public ExamActivityListRes list(int pageNo, int pageSize) {
        //老师id
        Long memberId = TenantMemberHolder.get().getMemberId();
        //当前作为考试的管理角色
        List<ExamMemberRoleEntity> memberList = examMemberRoleDao.selectByMemberId(memberId);
        //当前老师 作为考试考官的 考试ids 查询
        List<Long> ids = examIdsByManage(memberId);
        //查询未发布
        List<ExamActivityEntity> noRelease = new ArrayList<>();
        //未开始
        List<ExamActivityEntity> noStarted = new ArrayList<>();
        //进行中
        List<ExamActivityEntity> process = new ArrayList<>();
        //已结束 需要处理分页
        Page<ExamActivityEntity> page = new Page<>();
        List<ExamActivityEntity> finishes = new ArrayList<>();
        List<CountResmap> countResmaps = new ArrayList<>(NumberUtils.INTEGER_ONE);
        if (CollectionUtils.isNotEmpty(ids)) {
            //获取题目数量
            countResmaps = examTopicDao.getCntByExamIds(ids);
            //获取考试数据
            List<ExamActivityEntity> allList = examActivityDao.selByIdsAndStatus(ids, null);
            //查询未发布  创建时间降序 examActivityDao.selByIdsAndStatus(ids, ExamStatusEnum.NO_RELEASE.getValue());
            noRelease = allList.stream().filter(e->ExamStatusEnum.NO_RELEASE.getValue().equals(e.getPublishStatus())).sorted(Comparator.comparing(ExamActivityEntity::getInsertTime).reversed()).collect(Collectors.toList());
            //未发布的创建时间降序       noRelease = noRelease.stream().collect(Collectors.toList());
            //未开始examActivityDao.selByIdsAndStatus(ids, ExamStatusEnum.NO_STARTED.getValue());
            noStarted =allList.stream().filter(e->ExamStatusEnum.NO_STARTED.getValue().equals(e.getPublishStatus())).collect(Collectors.toList());
            //进行中 examActivityDao.selByIdsAndStatus(ids, ExamStatusEnum.PROCESS.getValue());
            process = allList.stream().filter(e->ExamStatusEnum.PROCESS.getValue().equals(e.getPublishStatus())).collect(Collectors.toList());
            //已结束 需要处理分页
            page = PageHelper.startPage(pageNo, pageSize);
            finishes = examActivityDao.selByIdsAndStatus(ids, ExamStatusEnum.FINISH.getValue());
        }
        BasePageRes<ExamActivityListInfoRes> finishList = BasePageRes.newBuilder(examActivityListData(memberId, finishes, memberList, countResmaps))
                .addPageNo(pageNo)
                .addPageSize(pageSize)
                .addTotal(page.getTotal())
                .build();
        return ExamActivityListRes.builder()
                .noReleaseList(examActivityListData(memberId, noRelease, memberList, countResmaps))
                .noStartList(examActivityListData(memberId, noStarted, memberList, countResmaps))
                .processList(examActivityListData(memberId, process, memberList, countResmaps))
                .finishList(finishList)
                .build();
    }

    /**
     * 查询出 当前memberId 作为考官的 考试ids集合
     */
    public List<Long> examIdsByManage(Long memberId){
        List<Long> ids = new ArrayList<>();
        List<ExamMemberRoleEntity> memberList = examMemberRoleDao.selectByMemberId(memberId);
        //我是成员组的考试ids
        List<Long> noAuthIds = memberList.stream().map(ExamMemberRoleEntity::getExamId).collect(Collectors.toList());
        //我是管理员的考试
        List<ExamActivityEntity> authList = examActivityDao.selectByMemberId(memberId.toString());
        List<Long> authIds = authList.stream().map(ExamActivityEntity::getId).collect(Collectors.toList());
        ids.addAll(noAuthIds);
        ids.addAll(authIds);
        return ids;
    }



    /**
     * 考试列表 绑定角色信息
     *
     * @param memberId
     * @param list
     * @param memberList
     * @return
     */
    List<ExamActivityListInfoRes> examActivityListData(Long memberId, List<ExamActivityEntity> list, List<ExamMemberRoleEntity> memberList, List<CountResmap> countResmaps) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<Long> examIds = list.stream().map(ExamActivityEntity::getId).collect(Collectors.toList());

        // 批量获取考试成员
        List<ExamStudentDTO> examStudents = this.examActivityStudentDao.selectByExamIds(examIds);

        // key - 考试id; value - 考试成员id集合
        Map<Long, List<Long>> examStudentIdsMap = examStudents.parallelStream().collect(Collectors.groupingBy(ExamStudentDTO::getExamId, Collectors.mapping(ExamStudentDTO::getMemberId, Collectors.toList())));

        // 所有考试成员id集合
        List<Long> allExamStudentIds = examStudents.parallelStream().map(ExamStudentDTO::getMemberId).distinct().collect(Collectors.toList());
        // 所有考试成员信息
        List<TenantMemberPvdRes> tenantMembers = this.tenantMemberIntegration.listByMemberId(allExamStudentIds);
        // 保留有效成员，过滤无效停用的成员
        List<Long> validMemberIds = tenantMembers.parallelStream().filter(new Predicate<TenantMemberPvdRes>() {
            @Override
            public boolean test(TenantMemberPvdRes entity) {
                return YesNoEnum.YES.getValue().equals(entity.getValidFlag());
            }
        }).map(TenantMemberPvdRes::getId).collect(Collectors.toList());

        examStudentIdsMap = examStudentIdsMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, new Function<Map.Entry<Long, List<Long>>, List<Long>>() {
            @Override
            public List<Long> apply(Map.Entry<Long, List<Long>> entry) {
                List<Long> ids = entry.getValue();
                ids.retainAll(validMemberIds);
                return ids;
            }
        }));

        //计算考试列表  提交人数
        List<CountResmap> countStuSubmitList = examStudentAnswerDao.selCountSubmitByExamIds(examIds);
        //计算已批改人数
        List<CountResmap> countStuCorrectList = examStudentAnswerDao.selCountCorrectByExamIds(examIds);

        Map<Long, List<Long>> finalExamStudentIdsMap = examStudentIdsMap;
        return list.stream().map(e -> {
            List<String> roles = new ArrayList<>();
            //查询当前用户是否在当前考试成员组中
            List<ExamMemberRoleEntity> entityRoles = memberList.stream().filter(v -> v.getMemberId().equals(memberId) && v.getExamId().equals(e.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(entityRoles)) {
                roles.add(ExamMemberRoleEnum.AUTHOR.getValue());
            } else {
                roles = entityRoles.stream().map(ExamMemberRoleEntity::getRole).collect(Collectors.toList());
            }
            Long topicNum = NumberUtils.LONG_ZERO;
            //统计试卷题目数量
            if (CollectionUtils.isNotEmpty(countResmaps)) {
                CountResmap countResmap = countResmaps.stream().filter(y -> y.getId().equals(e.getId())).findAny().orElse(null);
                if (countResmap != null) {
                    topicNum = countResmap.getCnt();
                }
            }
            //计算剩余时间
            LocalDateTime endDateTime = DateHelper.instance().ofEpochMilli2DateTime(e.getEndMills());
            LocalDateTime currentTime = DateHelper.instance().millisTimestamp2LocalDateTime(DateHelper.instance().currentMills());

            int lateLimit = e.getLateLimit();

            Integer fillTimeStatus = e.getFillTimeStatus();

            int timeLimit = 0;
            long time = ChronoUnit.MINUTES.between(currentTime, endDateTime);
            if (time > 0) {
                timeLimit = (int) time;
            }

            List<Long> studentIds = finalExamStudentIdsMap.get(e.getId());
            int allCount = CollectionUtils.isNotEmpty(studentIds) ? studentIds.size() : 0;
            CountResmap submitCount = countStuSubmitList.stream().filter(x -> x.getId() != null && x.getId().equals(e.getId())).findAny().orElse(null);
            CountResmap correctCount = countStuCorrectList.stream().filter(x -> x.getId() != null && x.getId().equals(e.getId())).findAny().orElse(null);

            int submitCnt = submitCount == null ? NumberUtils.INTEGER_ZERO : (int) submitCount.getCnt();
            int correctedCnt = correctCount == null ? NumberUtils.INTEGER_ZERO : (int) correctCount.getCnt();
            int correctStatus = 0;
            //批改状态，批改人数与全部人数一致为已批改
            if(allCount == correctedCnt){
                correctStatus = 2;
            }else if(correctedCnt == 0){
                //批改人数为0 为未批改
                correctStatus = 0;
            }else{
                correctStatus = 1;
            }
            return ExamActivityListInfoRes.builder()
                    .id(e.getId())
                    .name(e.getName())
                    .startMills(e.getStartMills())
                    .examTotalTime(e.getTimeLimit())
                    .timeLimit(e.getPublishStatus().equals(ExamStatusEnum.PROCESS.getValue()) ?
                            (NumberUtils.INTEGER_ONE.equals(fillTimeStatus) ? timeLimit + lateLimit : timeLimit) : e.getTimeLimit())
                    .allCount(allCount)
                    .submitCount(submitCnt)
                    .correctedCnt(correctedCnt)
                    .correctStatus(correctStatus)
                    .topicNum(topicNum)
                    .role(roles)
                    .build();
        }).collect(Collectors.toList());
    }


    /**
     * 查看考试配置  信息
     *
     * @param id
     * @return
     */
    @Override
    public ExamActivityConfigRes config(Long id) {
        ExamActivityEntity examActivityEntity = examActivityDao.selectById(id);
        return BeanHelper.copyObject(examActivityEntity, ExamActivityConfigRes.class);
    }


    /**
     * @param examActivityDetailReq
     * @return
     * @Description: 老师-考试配置更新
     * <AUTHOR>
     * @date 2020-12-01 17:00:11
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveConfig(Long id, ExamActivityDetailReq examActivityDetailReq) {
        // 1. 获取考试信息
        ExamActivityEntity examActivityEntity = examActivityDao.selectById(id);
        // 2. 当前考试已开始，且进行了修改时间，抛出异常
        if(ExamStatusEnum.PROCESS.getValue().equals(examActivityEntity.getPublishStatus()) && DateHelper.instance().currentMills() > examActivityEntity.getStartMills() && !examActivityDetailReq.getStartMills().equals(examActivityEntity.getStartMills())){
            throw EXAM_PROCESS_ERROR.error();
        }

        // v261 增加按题型乱序
        String unorderedType = examActivityDetailReq.getUnorderedType();
        if (ExamUnorderedTypeEnum.YES.getValue().equals(unorderedType)) {
            // 当按题型乱序时候，题目则不为乱序
            examActivityDetailReq.setUnorderedTopic(ExamUnorderedTypeEnum.NO.getValue());
        }
        // 题目乱序
        String unorderedTopic = examActivityDetailReq.getUnorderedTopic();
        if (ExamUnorderedTypeEnum.YES.getValue().equals(unorderedTopic)) {
            // 当按题目乱序时候，题型则不为乱序
            examActivityDetailReq.setUnorderedType(ExamUnorderedTypeEnum.NO.getValue());
        }

        BeanHelper.copyPropertiesIgnoreNull(examActivityDetailReq, examActivityEntity);
        //计算并设置结束时间
        Long limitTime = examActivityDetailReq.getTimeLimit() * 60 * 1000L;
        Long startMills = examActivityDetailReq.getStartMills();
        Long endMills = startMills + limitTime;
        examActivityEntity.setEndMills(endMills);
        examActivityDao.updateById(examActivityEntity);
        if (ExamStatusEnum.NO_STARTED.getValue().equals(examActivityEntity.getPublishStatus())) {
            //未开始
            redisRepository.zAdd(Constants.EXAM_NOSTART_Z,
                    examActivityEntity.getId().toString(),
                    (double) examActivityEntity.getStartMills());
            //进行redis覆盖
            long now = DateHelper.instance().currentMills();
            redisRepository.setEx(Constants.EXAM_ID + examActivityEntity.getId(),
                    examActivityEntity.getId().toString(),
                    examActivityEntity.getStartMills() - now,
                    TimeUnit.MILLISECONDS);

            // 考试开始时间与考试创建时间的间隔
            long intervalMills = startMills - now;
            // 如果考试开始时间与当前时间间隔大于两小时
            if (intervalMills > Constants.TWO_HOURS_MILLS) {
                // 考试前10分钟失效key
                String beforeTen = String.format(Constants.BEFORE_TEN_MINUTES_EXAM, id);
                redisRepository.setEx(beforeTen,
                        id.toString(),
                        startMills - Constants.TEN_MINUTES_MILLS - now,
                        TimeUnit.MILLISECONDS);

                // 考试前2小时失效key
                String beforeTwo = String.format(Constants.BEFORE_TWO_HOURS_EXAM, id);
                redisRepository.setEx(beforeTwo,
                        id.toString(),
                        startMills - Constants.TWO_HOURS_MILLS - now,
                        TimeUnit.MILLISECONDS);
            }
            // 如果考试开始时间与当前间隔小于两小时大于十分钟
            if (Constants.TEN_MINUTES_MILLS < intervalMills && intervalMills < Constants.TWO_HOURS_MILLS) {
                // 考试前10分钟失效key
                String beforeTen = String.format(Constants.BEFORE_TEN_MINUTES_EXAM, id);
                redisRepository.setEx(beforeTen, id.toString(), startMills - Constants.TEN_MINUTES_MILLS - now, TimeUnit.MILLISECONDS);
            }
        }
    }


    /**
     * 考卷题目统计信息+题目列表
     *
     * @param id
     * @return
     */
    @Override
    public ExamActivityStatisticsRes details(Long id) {

        //试卷基础信息
        ExamActivityEntity examActivityEntity = examActivityDao.selectById(id);
        ExamActivityBaseRes examActivityBaseRes = ExamActivityBaseRes.builder()
                .id(id)
                .name(examActivityEntity.getName())
                .insertTime(examActivityEntity.getInsertTime())
                .build();
        //试卷统计信息
        List<Long> ids = new ArrayList<>(1);
        ids.add(id);
        List<ExamTopicEntity> topics = examTopicDao.selByPaperIds(ids);
        //初始化统计  题型key值
        Set<String> allTypes = Arrays.stream(TestTopicTypeEnum.values()).map(TestTopicTypeEnum::getSqlValue).collect(Collectors.toSet());
        Map<String, Integer> scoreTopics = allTypes.parallelStream().collect(Collectors.toMap(Function.identity(), s -> NumberUtils.INTEGER_ZERO));
        Map<String, Long> countTopics = allTypes.parallelStream().collect(Collectors.toMap(Function.identity(), s -> NumberUtils.LONG_ZERO));

        if (org.springframework.util.CollectionUtils.isEmpty(topics)) {
            //没有题目
            return ExamActivityStatisticsRes.builder()
                    .info(examActivityBaseRes)
                    .countTopics(countTopics)
                    .scoreTopics(scoreTopics)
                    .score(NumberUtils.INTEGER_ZERO)
                    .count(NumberUtils.LONG_ZERO)
                    .build();
        }
        Map<String, Integer> scores = topics.stream()
                .collect(Collectors.groupingBy(ExamTopicEntity::getType,
                        Collectors.summingInt(ExamTopicEntity::getScore)));
        Map<String, Long> counts = topics.stream()
                .collect(Collectors.groupingBy(ExamTopicEntity::getType,
                        Collectors.counting()));
        //put有统计的进行覆盖初始化值
        scoreTopics.putAll(scores);
        countTopics.putAll(counts);
        //总分、总题数
        Integer scoreSum = topics.stream().mapToInt(ExamTopicEntity::getScore).sum();
        Long countSum = (long) topics.size();

        //试卷试题列表 查询选项进行处理 选项列表
        Map<Long, List<ExamTopicOptionEntity>> optionsGroup = optionsData(topics);

        //根据题目ids，获取知识点相关数据
        List<TopicKnowledgePvdRes> topicKnowledgeByTopicIds = topicKnowledgeIntegration.getTopicKnowledgeByTopicIds(topics.stream().map(ExamTopicEntity::getId).collect(Collectors.toList()));

        List<ExamTopicInfoRes> resultTopics = topics.stream().map(
                e -> {
                    List<OptionRes> options = new ArrayList<>();
                    List<TopicKnowledgePvdRes> topicKnowledgePvdRes = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(topicKnowledgeByTopicIds)) {
                        topicKnowledgePvdRes = topicKnowledgeByTopicIds.stream().filter(topicKnowledge -> e.getId().equals(topicKnowledge.getTopicId())).collect(Collectors.toList());
                    }
                    if (!CollectionUtils.isEmpty(optionsGroup.get(e.getId()))) {
                        options = optionsGroup.get(e.getId()).stream().map(x -> OptionRes.builder()
                                .id(x.getId())
                                .flag(x.getFlag())
                                .option(x.getOption())
                                .build()
                        ).collect(Collectors.toList());
                    }
                    return ExamTopicInfoRes.builder()
                            .id(e.getId())
                            .name(e.getName())
                            .type(e.getType())
                            .sort(e.getSort())
                            .score(e.getScore())
                            .answer(e.getAnswer())
                            .analysis(e.getAnalysis())
                            .options(options)
                            .knowledgeIds(topicKnowledgePvdRes)
                            // v220 增加难度评级
                            .complexity(e.getComplexity())
                            .build();
                }).collect(Collectors.toList());
        return ExamActivityStatisticsRes.builder()
                .info(examActivityBaseRes)
                .countTopics(countTopics)
                .scoreTopics(scoreTopics)
                .score(scoreSum)
                .count(countSum)
                .topics(resultTopics)
                .build();
    }

    @Override
    public List<ExamTopicInfoRes> detailsByType(Long id) {

        // 1. 试卷基础信息
        ExamActivityEntity examActivityEntity = examActivityDao.selectById(id);
        if (!Optional.ofNullable(examActivityEntity).isPresent()) {
            return new ArrayList<>();
        }

        // 2. 根据试卷id查询题目列表，按照题型排序
        List<ExamTopicEntity> topics = examTopicDao.getListByExamIdAndType(id);
        // 试卷没有题目
        if (CollectionUtils.isEmpty(topics)) {
            return new ArrayList<>();
        }

        // 试卷试题列表 查询选项进行处理 选项列表
        Map<Long, List<ExamTopicOptionEntity>> optionsGroup = optionsData(topics);

        // 3. 根据题目ids，获取知识点相关数据
        List<TopicKnowledgePvdRes> topicKnowledgeByTopicIds = topicKnowledgeIntegration.getTopicKnowledgeByTopicIds(topics.stream().map(ExamTopicEntity::getId).collect(Collectors.toList()));

        List<ExamTopicInfoRes> examTopicInfoResList = topics.stream().map(
                e -> {
                    List<OptionRes> options = new ArrayList<>();
                    // 通过topicId查找知识点
                    List<TopicKnowledgePvdRes> topicKnowledgePvdRes = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(topicKnowledgeByTopicIds)) {
                        topicKnowledgePvdRes = topicKnowledgeByTopicIds.stream().filter(topicKnowledge -> e.getId().equals(topicKnowledge.getTopicId()))
                                .collect(Collectors.toList());
                    }
                    if (!CollectionUtils.isEmpty(optionsGroup.get(e.getId()))) {
                        options = optionsGroup.get(e.getId()).stream().map(x -> OptionRes.builder()
                                .id(x.getId())
                                .flag(x.getFlag())
                                .option(x.getOption())
                                .build()
                        ).collect(Collectors.toList());
                    }
                    return ExamTopicInfoRes.builder()
                            .id(e.getId())
                            .name(e.getName())
                            .type(e.getType())
                            .sort(e.getSort())
                            .score(e.getScore())
                            .answer(e.getAnswer())
                            .analysis(e.getAnalysis())
                            .options(options)
                            .knowledgeIds(topicKnowledgePvdRes)
                            .insertTime(e.getInsertTime())
                            .build();
                    // 自定义排序规则
                }).collect(Collectors.toList());

        AtomicInteger atomicInteger = new AtomicInteger(0);
        List<ExamTopicEntity> examTopicEntityList = examTopicInfoResList.stream()
                .map(e -> {
                    ExamTopicEntity build = ExamTopicEntity.builder()
                            .sort(atomicInteger.getAndAdd(1))
                            .build();
                    build.setId(e.getId());
                    return build;
                })
                .collect(Collectors.toList());

        examTopicDao.updateBatchSelective(examTopicEntityList);
        return examTopicInfoResList;
    }

    /**
     * 查询题目选项 并根据题目分组  (重复代码提取)
     *
     * @param topics
     * @return
     */
    private Map<Long, List<ExamTopicOptionEntity>> optionsData(List<ExamTopicEntity> topics) {
        List<Long> topicIds = topics.stream().map(ExamTopicEntity::getId).collect(Collectors.toList());
        List<ExamTopicOptionEntity> options = examTopicOptionDao.selectByTopicIds(topicIds);
        return options.stream().collect(Collectors.groupingBy(ExamTopicOptionEntity::getTopicId));
    }

    /**
     * 新增题目
     *
     * @param examTopicInfoReq
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisDistributedLock(keys = {
            "T(java.lang.String).format(T(com.ioie.tac.web.constant.Constants).LOCK_UPDATE_TOPICKNOWLEDGE_KNOWLEDGEID, #examTopicInfoReq.examId)"})
    public BaseRes addTopic(ExamTopicInfoReq examTopicInfoReq) {
        //判定权限
        examMemberRoleService.checkPermission(examTopicInfoReq.getExamId(),ExamMemberRoleEnum.UPDATE.getValue(),ExamModuleEnum.TOPIC.getValue());
        BaseRes baseRes=new BaseRes();
        int sort=searchSort(examTopicInfoReq.getExamId(), examTopicDao);
        //创建题目
        Long topicId = ContextHelper.getBean(SnowflakeIdWorker.class).nextId();
        ExamTopicEntity examTopicEntity = ExamTopicEntity.builder()
                .examId(examTopicInfoReq.getExamId())
                .name(examTopicInfoReq.getName())
                .answer(examTopicInfoReq.getAnswer())
                .analysis(examTopicInfoReq.getAnalysis())
                .createType(TopicCreateTypeEnum.MANUAL.getName())
                .score(examTopicInfoReq.getScore())
                .sort(sort)
                .type(examTopicInfoReq.getType())
                .oriTopicId(NumberUtils.LONG_ZERO)
                // v220 增加难度评级
                .complexity(examTopicInfoReq.getComplexity())
                .build();
        examTopicEntity.setId(topicId);
        examTopicDao.insert(examTopicEntity);
        if (org.springframework.util.CollectionUtils.isEmpty(examTopicInfoReq.getOptions())) {
            //选项列表为空  是单选多选时抛出异常
            if (examTopicInfoReq.getType().equals(TestTopicTypeEnum.SINGLECHOICE.getSqlValue())
                    || examTopicInfoReq.getType().equals(TestTopicTypeEnum.MULTIPLECHOICE.getSqlValue())) {
                throw ExamActivityError.NEED_OPTION.error();
            } else {
                if (StringUtils.isEmpty(examTopicInfoReq.getAnswer())) {
                    throw ExamActivityError.NO_ANSWER.error();
                }
            }
        } else {
            optionData(topicId, examTopicInfoReq.getOptions());
        }
        // 添加知识点
        List<Long> knowledgeIds = examTopicInfoReq.getKnowledgeIds();
        if (!knowledgeIds.isEmpty()) {
            int i = topicKnowledgeIntegration.insertOne(knowledgeIds, topicId);
        }
        baseRes.setId(topicId);
        return baseRes;
    }

    /**
     * 根据试卷id获取试题的排序值
     *
     * @param id
     * @return
     */
    public static int searchSort(Long id, ExamTopicDao examTopicDao){
        //查询列表 找到最大sort值
        List<Long> paperIds = new ArrayList<>();
        paperIds.add(id);
        List<ExamTopicEntity> topics = examTopicDao.selByPaperIds(paperIds);
        int sort = NumberUtils.INTEGER_ONE;
        if (!org.springframework.util.CollectionUtils.isEmpty(topics)) {
            sort += topics.stream().mapToInt(ExamTopicEntity::getSort).max().getAsInt();
        }
        return sort;
    }


    /**
     * 编辑题目
     *
     * @param id
     * @param examTopicInfoReq
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTopic(Long id, ExamTopicInfoReq examTopicInfoReq) {
        //判定权限
        examMemberRoleService.checkPermission(examTopicInfoReq.getExamId(), ExamMemberRoleEnum.UPDATE.getValue(), ExamModuleEnum.TOPIC.getValue());
        //更新题目信息
        ExamTopicEntity examTopicEntity = examTopicDao.selectById(id);
        examTopicEntity.setName(examTopicInfoReq.getName());
        examTopicEntity.setScore(examTopicInfoReq.getScore());
        examTopicEntity.setType(examTopicInfoReq.getType());
        examTopicEntity.setAnswer(examTopicInfoReq.getAnswer());
        examTopicEntity.setAnalysis(examTopicInfoReq.getAnalysis());
        // v220 增加难度评级
        examTopicEntity.setComplexity(examTopicInfoReq.getComplexity());
        examTopicDao.updateById(examTopicEntity);
        //原选项清除
        List<Long> topicIds = new ArrayList<>();
        topicIds.add(id);
        deleteOptions(topicIds);
        //新选项录入
        if (!org.springframework.util.CollectionUtils.isEmpty(examTopicInfoReq.getOptions())) {
            optionData(id, examTopicInfoReq.getOptions());
        }
        // 添加新知识点
        //topicKnowledgeIntegration.update(id, examTopicInfoReq.getKnowledgeIds());
    }

    /**
     * 题目选项录入
     *
     * @param topicId
     * @param optionList
     */
    public void optionData(Long topicId, List<OptionReq> optionList) {
        Long count = optionList.stream().filter(e -> e.getFlag().equals(Boolean.TRUE)).count();
        if (NumberUtils.LONG_ZERO.equals(count)) {
            //没有正确答案
            throw ExamActivityError.NEED_CORRECT_OPTION.error();
        }
        //选项录入
        AtomicInteger sortOption = new AtomicInteger(NumberUtils.INTEGER_ONE);
        List<ExamTopicOptionEntity> options = optionList.stream()
                .map(e ->
                        ExamTopicOptionEntity.builder()
                                .option(e.getOption())
                                .flag(e.getFlag())
                                .topicId(topicId)
                                .sort(sortOption.getAndAdd(NumberUtils.INTEGER_ONE))
                                .build()
                ).collect(Collectors.toList());


        examTopicOptionDao.batchInsert(options);
    }

    /**
     * 批量删除题目
     *
     * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTopics(List<Long> ids) {

        if (!org.springframework.util.CollectionUtils.isEmpty(ids)) {
            List<ExamTopicEntity> list = examTopicDao.selectBatchIds(ids);
            if (CollectionUtils.isNotEmpty(list)) {
                //判定权限
                examMemberRoleService.checkPermission(list.get(NumberUtils.INTEGER_ZERO).getExamId(), ExamMemberRoleEnum.UPDATE.getValue(), ExamModuleEnum.TOPIC.getValue());
            }
            ExamTopicEntity examTopicEntity = list.get(0);
            examTopicDao.deleteBatchIds(ids);
            //清除题目选项
            deleteOptions(ids);
            // 清除知识点
            topicKnowledgeIntegration.deleteByList(ids);
            // 删除 导入目标表中间表数据
            List<Long> oriTopicList = list.parallelStream()
                    .map(ExamTopicEntity::getOriTopicId)
                    .collect(Collectors.toList());
            // 考试id
            Long examId = examTopicEntity.getExamId();
            List<TopicExamTargetEntity> listTargetIdAndTopicIds = topicExamTargetDao.getListTargetIdAndTopicIds(examId, oriTopicList);
            // 删除 从考试中导入目标的数据
            if(CollectionUtils.isNotEmpty(listTargetIdAndTopicIds)){
                List<Long> targetIds = listTargetIdAndTopicIds.parallelStream()
                        .map(TopicExamTargetEntity::getId)
                        .collect(Collectors.toList());
                topicExamTargetDao.deleteBatchIds(targetIds);
            }
            TargetTopicSelectReq targetTopicSelectReq = new TargetTopicSelectReq();
            targetTopicSelectReq.setTargetId(examId);
            targetTopicSelectReq.setTopicIds(oriTopicList);
            topicImportTargetIntegration.deleteTopicQuizTarget(targetTopicSelectReq);
            topicImportTargetIntegration.deleteTopicPackageTarget(targetTopicSelectReq);
            topicImportTargetIntegration.deleteTopicTestTarget(targetTopicSelectReq);
        }
    }

    /**
     * 清除题目选项
     *
     * @param topicIds
     */
    public void deleteOptions(List<Long> topicIds) {
        //判定原有题目选项是否存在
        List<ExamTopicOptionEntity> oldOptions = examTopicOptionDao.selectByTopicIds(topicIds);
        //原有选项清除
        if (!org.springframework.util.CollectionUtils.isEmpty(oldOptions)) {
            List<Long> oldOptionIds = oldOptions.stream().map(ExamTopicOptionEntity::getId).collect(Collectors.toList());
            examTopicOptionDao.deleteBatchIds(oldOptionIds);
        }
    }

    /**
     * 清除知识点, 废弃
     *
     * @param topicIds 题目id
     */
    public void deleteKnowledgeIds(List<Long> topicIds) {
//        // 判断是否存在知识点
//        List<TopicKnowledgeListPvdRes> topicKnowledgeListPvdRes = topicKnowledgeIntegration.listByTopicIds(topicIds);
//        if (!CollectionUtils.isEmpty(topicKnowledgeListPvdRes)) {
//            topicKnowledgeIntegration.deleteByList(topicIds);
//        }
    }

    /**
     * 批量改分
     *
     * @param examTopicScoreReq
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void scoreBatch(ExamTopicScoreReq examTopicScoreReq) {
        //判定权限
        examMemberRoleService.checkPermission(examTopicScoreReq.getExamId(), ExamMemberRoleEnum.UPDATE.getValue(), ExamModuleEnum.TOPIC.getValue());
        //判断开始结束题号 是否不在区间
        List<Long> paperIds = new ArrayList<>();
        paperIds.add(examTopicScoreReq.getExamId());
        List<ExamTopicEntity> list = examTopicDao.selByPaperIds(paperIds);
        int size = list.size();
        int begin = examTopicScoreReq.getBegin();
        int end = examTopicScoreReq.getEnd();
        if (begin > size || end > size) {
            //抛出异常
            throw ExamActivityError.NO_NOT_VALID.error();
        }
        //计数
        List<Long> topicIds = list.subList(begin - NumberUtils.INTEGER_ONE, end).stream().map(ExamTopicEntity::getId).collect(Collectors.toList());
        //根据开始结束id进行赋值返回ids
        examTopicDao.scoreBatch(topicIds, examTopicScoreReq.getScore());
    }

    /**
     * 批量排序
     *
     * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sortBatch(List<Long> ids) {
        List<ExamTopicEntity> topics = examTopicDao.selectBatchIds(ids);
        if (!org.springframework.util.CollectionUtils.isEmpty(topics)) {

            List<ExamTopicEntity> list = examTopicDao.selectBatchIds(ids);
            if (CollectionUtils.isNotEmpty(list)) {
                //判定权限
                examMemberRoleService.checkPermission(list.get(NumberUtils.INTEGER_ZERO).getExamId(), ExamMemberRoleEnum.UPDATE.getValue(), ExamModuleEnum.TOPIC.getValue());
            }


            AtomicInteger sort = new AtomicInteger(NumberUtils.INTEGER_ONE);
            //倒叙
//            ids = Lists.reverse(ids);
            List<ExamTopicEntity> topicSortList = ids.stream().map(
                    e -> {
                        ExamTopicEntity examTopicEntity = ExamTopicEntity.builder().build();
                        ExamTopicEntity oldTopic = topics.stream().filter(v -> v.getId().equals(e)).findAny().orElse(null);
                        BeanHelper.copyPropertiesIgnoreNull(oldTopic, examTopicEntity);
                        examTopicEntity.setSort(sort.getAndAdd(NumberUtils.INTEGER_ONE));
                        return examTopicEntity;
                    }
            ).collect(Collectors.toList());
            //批量更新
            examTopicDao.updateBatch(topicSortList);
        }
    }

    /**
     * excel导入题目
     *
     * @param examId
     */
    @Override
    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    public void importTopics(Long examId, MultipartFile file) {
        //判定权限
        examMemberRoleService.checkPermission(examId, ExamMemberRoleEnum.UPDATE.getValue(), ExamModuleEnum.TOPIC.getValue());

        //文件 easyPoi 转换对象
        ExcelImportResult<ExamTopicExcelReq> importResult = ExcelUtils.importExcelCheck(file, NumberUtils.INTEGER_ZERO,NumberUtils.INTEGER_ONE, ExamTopicExcelReq.class);
        Workbook workbook = importResult.getWorkbook();
        Sheet sheet = workbook.getSheetAt(0);
        int lastRowNum = sheet.getLastRowNum();
        int sort=searchSort(examId, examTopicDao);
        //注解校验数据有误进行输出返回
        if (importResult.isVerifyFail()) {
            //抛出异常
            String throwMsg = "";
            List<ExamTopicExcelReq> failList = importResult.getFailList();
            for (ExamTopicExcelReq excelReq : failList) {
                Integer rowNum = excelReq.getRowNum()+NumberUtils.INTEGER_ONE;
                String errorMsg = excelReq.getErrorMsg();
                throwMsg += "第" + rowNum + "行，" +errorMsg+"!";
            }
            throw ExamActivityError.EXCEL_NO_DATA.error(throwMsg);
        }
        List<ExamTopicOptionEntity> examTopicOptionEntities = new ArrayList<>();
        AtomicInteger sortTopic = new AtomicInteger(sort);
        List<ExamTopicEntity> examTopicEntities = importResult.getList().stream().map(e -> {
            String answer = e.getAnswer();
            if (TestTopicTypeEnum.SINGLECHOICE.getValue().equals(e.getType())
                    || TestTopicTypeEnum.MULTIPLECHOICE.getValue().equals(e.getType())) {
                answer = "";
            }
            ExamTopicEntity res = ExamTopicEntity.builder()
                    .examId(examId)
                    .oriTopicId(NumberUtils.LONG_ZERO)
                    .type(TestTopicTypeEnum.getTopicTypeSqlValue(e.getType()))
                    .createType(TopicCreateTypeEnum.IMPORT_TEST.getName())
                    .sort(sortTopic.getAndAdd(NumberUtils.INTEGER_ONE))
                    .score(e.getScore().intValue())
                    //导入富文本字段需转义处理
                    .name(changeStr(e.getName()))
                    .answer(changeStr(answer))
                    .analysis(changeStr(e.getAnalysis()))
                    .complexity(NumberUtils.INTEGER_ONE)
                    .build();
            Long id = ContextHelper.getBean(SnowflakeIdWorker.class).nextId();
            res.setId(id);

            if (TestTopicTypeEnum.SINGLECHOICE.getValue().equals(e.getType())
                    || TestTopicTypeEnum.MULTIPLECHOICE.getValue().equals(e.getType())) {

                String[] options = TopicOptionEnum.OPTION.getArr();
                int i = NumberUtils.INTEGER_ZERO;
                for (String str : options) {
                    //选项为空 进入下一层循环
                    boolean flag = e.getAnswer().contains(str);
                    String option = ReflectionUtils.getFieldValue(e, TopicOptionEnum.OPTION.getName() + str);
                    if (StringUtils.isNotEmpty(option)) {
                        examTopicOptionEntities.add(ExamTopicOptionEntity.builder()
                                .topicId(id)
                                .option(option)
                                .flag(flag)
                                .sort(i)
                                .build());
                        i++;
                    } else {
                        continue;
                    }
                }

            }
            return res;
        }).collect(Collectors.toList());
        //批量插入
        if (!org.springframework.util.CollectionUtils.isEmpty(examTopicEntities)) {
            examTopicDao.batchInsert(examTopicEntities);
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(examTopicOptionEntities)) {
            examTopicOptionDao.batchInsert(examTopicOptionEntities);
        }
    }


    /**
     * 导入 需要处理富文本字段转义
     */
    public static String changeStr(String str) {

        if (!org.springframework.util.StringUtils.isEmpty(str)) {
            str = str.replaceAll("&", "&amp;").replaceAll("<", "&lt;").replaceAll(">", "&gt;").replaceAll("\"", "&quot;");
            return str.replaceAll("\n", "<br>").replaceAll(" ", "&nbsp;");
        }

        return "";
    }


    /**
     * @param id
     * @return
     * @Description: 老师-发布考试
     * <AUTHOR>
     * @date 2020-12-02 11:50:11
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void release(Long id) {
        //判定权限  判定满足对应权限 -1 只判定角色 是管理员
        examMemberRoleService.checkPermission(id, ExamMemberRoleEnum.AUTHOR.getValue(), NumberUtils.INTEGER_MINUS_ONE);
        ExamActivityEntity examActivityEntity = examActivityDao.selectById(id);
        //没有开始结束时间
        Long startMills = examActivityEntity.getStartMills();
        long now = DateHelper.instance().currentMills();
        if (NumberUtils.LONG_ZERO.equals(startMills)) {
            throw ExamActivityError.NO_CONFIG.error();
        } else if (startMills < now) {
            //小于当前时间
            throw ExamActivityError.CONFIG_START_LESS.error();
        }
        //判定有没有题目
        List<ExamTopicEntity> topics = examTopicDao.getListByExamId(id);
        if (CollectionUtils.isEmpty(topics)) {
            throw ExamActivityError.NO_TOPICS.error();
        }
        //根据时间判定 调整状态为 未开始、进行中
        if (startMills > now) {
            //未开始
            examActivityEntity.setPublishStatus(ExamStatusEnum.NO_STARTED.getValue());
            //未开始
            redisRepository.zAdd(Constants.EXAM_NOSTART_Z, examActivityEntity.getId().toString(), (double) startMills);
            redisRepository.setEx(Constants.EXAM_ID + examActivityEntity.getId(),
                    examActivityEntity.getId().toString(),
                    startMills - now,
                    TimeUnit.MILLISECONDS);
        } else {
            //进行中
            examActivityEntity.setPublishStatus(ExamStatusEnum.PROCESS.getValue());

            //补足时间 追加
            long lateMills = NumberUtils.LONG_ZERO;
            if (NumberUtils.INTEGER_ONE.equals(examActivityEntity.getFillTimeStatus())) {
                lateMills = examActivityEntity.getLateLimit() * 60 * 1000L;
            }
            //进行中
            redisRepository.zAdd(Constants.EXAM_PROCESS_Z,
                    examActivityEntity.getId().toString(),
                    (double) (examActivityEntity.getEndMills()));
            redisRepository.setEx(Constants.EXAM_ID + examActivityEntity.getId(),
                    examActivityEntity.getId().toString(),
                    examActivityEntity.getEndMills() - now + lateMills,
                    TimeUnit.MILLISECONDS);
        }

        //数据库中所有更新时间都和插入时间一样
        examActivityEntity.setUpdateTime(now);
        examActivityDao.updateById(examActivityEntity);

        // 正常发布考试
        getuiAsync.pushExamMessage(AppExamOrTestMessageEnum.NOTICE, id);

        // 考试开始时间与考试创建时间的间隔
        long intervalMills = startMills - now;
        // 如果考试开始时间与当前时间间隔大于两小时
        if (intervalMills > Constants.TWO_HOURS_MILLS) {
            // 考试前10分钟失效key
            String beforeTen = String.format(Constants.BEFORE_TEN_MINUTES_EXAM, id);
            redisRepository.setEx(beforeTen, id.toString(), startMills - Constants.TEN_MINUTES_MILLS - now, TimeUnit.MILLISECONDS);

            // 考试前2小时失效key
            String beforeTwo = String.format(Constants.BEFORE_TWO_HOURS_EXAM, id);
            redisRepository.setEx(beforeTwo, id.toString(), startMills - Constants.TWO_HOURS_MILLS - now, TimeUnit.MILLISECONDS);
        }
        // 如果考试开始时间与当前间隔小于两小时大于十分钟
        if (Constants.TEN_MINUTES_MILLS < intervalMills && intervalMills < Constants.TWO_HOURS_MILLS) {
            // 考试前10分钟失效key
            String beforeTen = String.format(Constants.BEFORE_TEN_MINUTES_EXAM, id);
            redisRepository.setEx(beforeTen, id.toString(), startMills - Constants.TEN_MINUTES_MILLS - now, TimeUnit.MILLISECONDS);
        }
    }


    /**
     * @param id
     * @return
     * @Description: 老师-取消考试
     * <AUTHOR>
     * @date 2020-12-02 12:00:11
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancer(Long id) {
        //判定权限  判定满足对应权限 -1 只判定角色 是管理员
        examMemberRoleService.checkPermission(id, ExamMemberRoleEnum.AUTHOR.getValue(), NumberUtils.INTEGER_MINUS_ONE);
        ExamActivityEntity examActivityEntity = examActivityDao.selectById(id);
        examActivityEntity.setPublishStatus(ExamStatusEnum.NO_RELEASE.getValue());
        examActivityDao.updateById(examActivityEntity);

        //取消时，删除redis中的数据
        redisRepository.zRemove(Constants.EXAM_NOSTART_Z, examActivityEntity.getId().toString());
        redisRepository.zRemove(Constants.EXAM_PROCESS_Z, examActivityEntity.getId().toString());

        // 取消考试给学生发送考试取消通知
        List<ExamActivityStudentEntity> examMemberList = examActivityStudentDao.selByExamId(id);
        if (CollectionUtils.isNotEmpty(examMemberList)) {
            // 获取考试下的成员ids
            List<Long> memberIds = examMemberList.parallelStream().map(ExamActivityStudentEntity::getMemberId).collect(Collectors.toList());
            getuiAsync.pushCancelExamMessage(memberIds, id);
        }
    }

    /**
     * @param id
     * @return
     * @Description: 老师-删除考试
     * <AUTHOR>
     * @date 2020-12-02 12:00:11
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public void delete(Long id) {
        //判定权限  判定满足对应权限 -1 只判定角色 是管理员
        examMemberRoleService.checkPermission(id, ExamMemberRoleEnum.AUTHOR.getValue(), NumberUtils.INTEGER_MINUS_ONE);
        examActivityDao.deleteById(id);
        // 删除消息中的消息
        List<Long> contentIds = new ArrayList<>();
        contentIds.add(id);
        appMessageDao.deleteByContentIds(contentIds);
    }

    /**
     * @return
     * @Description 题目上移下移
     * <AUTHOR>
     * @date 2020/11/26 14:30
     */
    @Override
    @Transactional(readOnly = false, rollbackFor = Exception.class)
    public void upOrDown(ExamTopicExReq examTopicExReq) {
        ExamTopicEntity examTopicEntity1 = examTopicDao.selectById(examTopicExReq.getId1());
        //判定权限
        examMemberRoleService.checkPermission(examTopicEntity1.getExamId(), ExamMemberRoleEnum.UPDATE.getValue(), ExamModuleEnum.TOPIC.getValue());
        ExamTopicEntity examTopicEntity2 = examTopicDao.selectById(examTopicExReq.getId2());
        int sort = examTopicEntity1.getSort();
        examTopicEntity1.setSort(examTopicEntity2.getSort());
        examTopicEntity2.setSort(sort);
        examTopicDao.updateById(examTopicEntity1);
        examTopicDao.updateById(examTopicEntity2);
    }


    /**
     * 老师-批阅试卷-进入批改
     *
     * @param id 列表上的考试结果id(答题卡id)
     * @return {@link ExamCheckInfoRes}
     * <AUTHOR>
     * @date 2020/12/3 9:18
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Klock(keys = {"#id"}, lockTimeoutStrategy = LockTimeoutStrategy.FAIL_FAST, waitTime = 0)
    public ExamCheckInfoRes checkQuizTaskDetail(Long id, Integer noCorrected,  Integer nextCheck) {
        //是否为点击批阅下一份试卷
        if(nextCheck != null && nextCheck == 1){
            //获取当前答题卡对应的学生
            ExamStudentAnswerEntity examStudentAnswerEntity = examStudentAnswerDao.selectById(id);
            Long examId = examStudentAnswerEntity.getExamId();
            //1.根据学生ids获取答题卡列表
            List<ExamStudentAnswerEntity> examStudentAnswerEntities = examStudentAnswerDao.getListByExamId(examId);
            examStudentAnswerEntities = examStudentAnswerEntities.stream().filter(e -> !TenantMemberHolder.get().getMemberId().equals(e.getMemberId())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(examStudentAnswerEntities)){
                throw NO_CORRECTED.error();
            }
            List<ExamStudentAnswerEntity> correctedList = examStudentAnswerEntities.stream().filter(e -> NumberUtils.INTEGER_ONE.equals(e.getCheckStatus())).collect(Collectors.toList());
            //若都批改，则返回提示，试卷已全部批阅完成，2s后返回列表页
            if(CollectionUtils.isNotEmpty(correctedList) && examStudentAnswerEntities.size() == correctedList.size()){
                throw NO_CORRECTED.error();
            }
            //将上一份数据设置未批
            examStudentAnswerEntities = examStudentAnswerEntities.stream()
                    .peek(e -> {
                        if(e.getMemberId().equals(examStudentAnswerEntity.getMemberId())){
                            e.setCheckStatus(0);
                        }
                    })
                    .collect(Collectors.toList());
            //过滤出未批改且已提交的数据
            examStudentAnswerEntities = examStudentAnswerEntities.stream()
                    .filter(e -> NumberUtils.INTEGER_ZERO.equals(e.getCheckStatus()) && NumberUtils.INTEGER_ONE.equals(e.getSubmitStatus()))
                    .collect(Collectors.toList());

            //获取下一个批阅用户
            List<Long> answerIds = examStudentAnswerEntities.stream().map(ExamStudentAnswerEntity::getId).collect(Collectors.toList());
            int indexOf = answerIds.indexOf(examStudentAnswerEntity.getId());

            //最后一个时，需取第一个
            if (examStudentAnswerEntities.size() == 1 && id.equals(examStudentAnswerEntities.get(0).getId())) {
                throw NO_CORRECTED.error();
            }
            if(examStudentAnswerEntities.size() == (indexOf + 1)){
                id = examStudentAnswerEntities.get(0).getId();
            }else{
                id = examStudentAnswerEntities.get(indexOf + 1).getId();
            }
        }

        //1.根据答题卡id获取答题卡详情
        ExamStudentAnswerEntity examStudentAnswerEntity = examStudentAnswerDao.selectById(id);
        if (examStudentAnswerEntity == null) {
            return ExamCheckInfoRes.builder().build();
        }
        Long memberId = examStudentAnswerEntity.getMemberId();

        ExamActivityEntity examActivityEntity = examActivityDao.selectById(examStudentAnswerEntity.getExamId());

        //学生成绩
        Double stuScore = examStudentAnswerEntity.getScore();
        Long examId = examStudentAnswerEntity.getExamId();
        List<Long> userIds = new ArrayList<>(NumberUtils.INTEGER_ONE);
        userIds.add(memberId);
        //2.根据学生id获取学生详情
        List<TenantMemberPvdRes> tenantMemberPvdRes = tenantMemberIntegration.listByMemberIds(userIds);
        //学生姓名
        String userName = tenantMemberPvdRes.get(NumberUtils.INTEGER_ZERO).getName();
        //学生头像
        String avatar = tenantMemberPvdRes.get(NumberUtils.INTEGER_ZERO).getAvatar();
        //学生学号
        String no = tenantMemberPvdRes.get(NumberUtils.INTEGER_ZERO).getNo();
        //3. 获取学生试题答案列表
        List<ExamStudentOptionEntity> examStudentOptionList = examStudentOptionDao.getListByExamIdAndMemberId(memberId, examId, id);

        // 获取题目列表
        List<ExamTopicEntity> examTopicEntityList = examTopicDao.getListByExamId(examId);
        if(CollectionUtils.isNotEmpty(examTopicEntityList)){
            List<Long> topicIds = examTopicEntityList.parallelStream()
                    .map(ExamTopicEntity::getId)
                    .collect(Collectors.toList());
            //标记  全是客观题直接状态为已批改, true 为 客观题
            boolean markFlag = examTopicEntityList.stream()
                    .anyMatch(e -> TestTopicTypeEnum.COMPLETION.getSqlValue().equals(e.getType())
                            || TestTopicTypeEnum.SHORTANSWER.getSqlValue().equals(e.getType()));
            Long finalId = id;
            if(CollectionUtils.isEmpty(examStudentOptionList)){
                List<ExamStudentOptionEntity> insertList = examTopicEntityList.parallelStream()
                        .map(e -> {
                            ExamStudentOptionEntity build = ExamStudentOptionEntity.builder()
                                    .answerId(finalId)
                                    .examId(examId)
                                    .memberId(memberId)
                                    .topicId(e.getId())
                                    .answer(Strings.EMPTY)
                                    .score((TestTopicTypeEnum.SINGLECHOICE.getSqlValue().equals(e.getType())
                                            || TestTopicTypeEnum.MULTIPLECHOICE.getSqlValue().equals(e.getType())
                                            || TestTopicTypeEnum.JUDGETOPIC.getSqlValue().equals(e.getType())) ? NumberUtils.DOUBLE_ZERO : NumberUtils.DOUBLE_MINUS_ONE)
                                    .checkStatus((TestTopicTypeEnum.SINGLECHOICE.getSqlValue().equals(e.getType())
                                            || TestTopicTypeEnum.MULTIPLECHOICE.getSqlValue().equals(e.getType())
                                            || TestTopicTypeEnum.JUDGETOPIC.getSqlValue().equals(e.getType())) ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_ZERO)
                                    .topicSymbol(NumberUtils.INTEGER_ZERO)
                                    .build();
                            build.setInsertUserId(examStudentAnswerEntity.getInsertUserId());
                            build.setUpdateUserId(examStudentAnswerEntity.getUpdateUserId());
                            build.setInsertUserName(examStudentAnswerEntity.getInsertUserName());
                            build.setUpdateUserName(examStudentAnswerEntity.getUpdateUserName());
                            build.setTenantId(examStudentAnswerEntity.getTenantId());
                            build.setDataValid(1);
                            build.setId(ContextHelper.getBean(SnowflakeIdWorker.class).nextId());
                            return build;
                        })
                        .collect(Collectors.toList());
                examStudentOptionDao.batchInsertV1(insertList);
                examStudentOptionList.addAll(insertList);
            }else if(topicIds.size() != examStudentOptionList.size()){
                List<Long> existList = examStudentOptionList.parallelStream()
                        .map(ExamStudentOptionEntity::getTopicId)
                        .collect(Collectors.toList());
                // 过滤出一些没有数据的题目
                List<ExamTopicEntity> insertTopics = examTopicEntityList.parallelStream()
                        .filter(e -> !existList.contains(e.getId()))
                        .collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(insertTopics)){
                    List<ExamStudentOptionEntity> insertList = insertTopics
                            .parallelStream()
                            .map(e -> {
                                ExamStudentOptionEntity build = ExamStudentOptionEntity.builder()
                                        .answerId(finalId)
                                        .examId(examId)
                                        .memberId(memberId)
                                        .topicId(e.getId())
                                        .answer(Strings.EMPTY)
                                        .score((TestTopicTypeEnum.SINGLECHOICE.getSqlValue().equals(e.getType())
                                                || TestTopicTypeEnum.MULTIPLECHOICE.getSqlValue().equals(e.getType())
                                                || TestTopicTypeEnum.JUDGETOPIC.getSqlValue().equals(e.getType())) ? NumberUtils.DOUBLE_ZERO : NumberUtils.DOUBLE_MINUS_ONE)
                                        .checkStatus((TestTopicTypeEnum.SINGLECHOICE.getSqlValue().equals(e.getType())
                                                || TestTopicTypeEnum.MULTIPLECHOICE.getSqlValue().equals(e.getType())
                                                || TestTopicTypeEnum.JUDGETOPIC.getSqlValue().equals(e.getType())) ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_ZERO)
                                        .topicSymbol(NumberUtils.INTEGER_ZERO)
                                        .build();
                                build.setInsertUserId(examStudentAnswerEntity.getInsertUserId());
                                build.setUpdateUserId(examStudentAnswerEntity.getUpdateUserId());
                                build.setInsertUserName(examStudentAnswerEntity.getInsertUserName());
                                build.setUpdateUserName(examStudentAnswerEntity.getUpdateUserName());
                                build.setTenantId(examStudentAnswerEntity.getTenantId());
                                build.setDataValid(1);
                                build.setId(ContextHelper.getBean(SnowflakeIdWorker.class).nextId());
                                return build;
                            })
                            .collect(Collectors.toList());
                    examStudentOptionDao.batchInsertV1(insertList);
                    examStudentOptionList.addAll(insertList);
                }
            }
            // 若全部为客观题
            if(!markFlag){
                //全是客观题  标记为已批改
                double sum = examStudentOptionList.stream()
                        .filter(t -> t.getScore() != null && !NumberUtils.DOUBLE_MINUS_ONE.equals(t.getScore()))
                        .mapToDouble(ExamStudentOptionEntity::getScore).sum();
                examStudentAnswerEntity.setScore(sum);
                examStudentAnswerEntity.setCheckStatus(NumberUtils.INTEGER_ONE);
                examStudentAnswerEntity.setCheckTime(DateHelper.instance().currentMills());
                String updateUserId = examActivityEntity.getUpdateUserId();
                examStudentAnswerEntity.setCheckUserId(Long.valueOf(updateUserId));
                examStudentAnswerDao.updateById(examStudentAnswerEntity);
            }
        }
        //3.1 设置批改信息,当所有题目都批改了，才设置批改完成
        List<ExamStudentOptionEntity> correctedTopicList = examStudentOptionList.stream().filter(e -> YesNoEnum.YES.getValue().equals(e.getCheckStatus())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(correctedTopicList) && examStudentOptionList.size() == correctedTopicList.size()){
            double sum = correctedTopicList.parallelStream()
                    .filter(e -> !NumberUtils.DOUBLE_MINUS_ONE.equals(e.getScore()))
                    .mapToDouble(ExamStudentOptionEntity::getScore)
                    .sum();
            examStudentAnswerEntity.setScore(sum);
            examStudentAnswerEntity.setCheckStatus(NumberUtils.INTEGER_ONE);
            examStudentAnswerEntity.setCheckTime(DateHelper.instance().currentMills());
            examStudentAnswerEntity.setCheckUserId(TenantMemberHolder.get().getMemberId());
            examStudentAnswerDao.updateById(examStudentAnswerEntity);
        }

        if (NumberUtils.DOUBLE_ZERO.equals(stuScore) && CollectionUtils.isNotEmpty(examStudentOptionList)) {
            stuScore = examStudentOptionList.stream().filter(e -> !NumberUtils.DOUBLE_MINUS_ONE.equals(e.getScore())).mapToDouble(ExamStudentOptionEntity::getScore).sum();
        }
        //4.根据考试id获取试题列表及实体总分

        //考试试题列表
        List<ExamCheckTopicInfoRes> examTopicListResList = new ArrayList<>(NumberUtils.INTEGER_ONE);
        double score = NumberUtils.DOUBLE_ZERO;
        //客观题总分
        double objectScore = NumberUtils.DOUBLE_ZERO;
        //主观题总分
        double subjectScore = NumberUtils.DOUBLE_ZERO;
        //学生客观题总分
        AtomicDouble stuObjectScore = new AtomicDouble(NumberUtils.DOUBLE_ZERO);
        //学生主观题总分
        AtomicDouble stuSubjectScore = new AtomicDouble(NumberUtils.DOUBLE_ZERO);

        if (CollectionUtils.isNotEmpty(examTopicEntityList)) {
            //根据考试id获取选项列表（单选或多选）
            List<Long> topicIds = getExamTopicIds(examTopicEntityList);
            //统计总分
            score = examTopicEntityList.stream().mapToDouble(ExamTopicEntity::getScore).sum();
            //统计客观题总分
            objectScore = examTopicEntityList.stream()
                                    .filter(e ->
                                            TestTopicTypeEnum.SINGLECHOICE.getSqlValue().equals(e.getType())
                            || TestTopicTypeEnum.MULTIPLECHOICE.getSqlValue().equals(e.getType())
                            || TestTopicTypeEnum.JUDGETOPIC.getSqlValue().equals(e.getType()))
                    .mapToDouble(ExamTopicEntity::getScore)
                    .sum();
            //统计主观题总分
            subjectScore = examTopicEntityList.stream()
                    .filter(e ->
                            TestTopicTypeEnum.COMPLETION.getSqlValue().equals(e.getType())
                            || TestTopicTypeEnum.SHORTANSWER.getSqlValue().equals(e.getType()))
                    .mapToDouble(ExamTopicEntity::getScore)
                    .sum();
            //按题目id分组
            Map<Long, List<ExamTopicOptionEntity>> topicOptionMap = new HashMap<>(16);
            if (CollectionUtils.isNotEmpty(topicIds)) {
                //存在单选/多选
                List<ExamTopicOptionEntity> examTopicOptionEntityList = examTopicOptionDao.getListByTopicIds(topicIds);
                topicOptionMap = examTopicOptionEntityList.stream().collect(Collectors.groupingBy(ExamTopicOptionEntity::getTopicId));
            }
            Map<Long, List<ExamTopicOptionEntity>> finalTopicOptionMap = topicOptionMap;
            AtomicInteger atomicInteger = new AtomicInteger(1);
            examTopicListResList = examTopicEntityList.stream()
                    .map(e -> {
                        ExamCheckTopicInfoRes examTopicListRes = BeanHelper.copyObject(e, ExamCheckTopicInfoRes.class);
                        examTopicListRes.setStuMark(ExamStuMarkEnum.NO_CORRECT.getValue());
                        String answer = "";
                        if (CollectionUtils.isNotEmpty(examStudentOptionList)) {
                            ExamStudentOptionEntity examStudentOptionEntity = examStudentOptionList.stream().filter(t -> t.getTopicId().equals(e.getId())).findAny().orElse(null);
                            if (examStudentOptionEntity != null) {
                                answer = examStudentOptionEntity.getAnswer();
                                examTopicListRes.setStuAnswer(answer);
                                //学生-题目得分
                                Double stuTopicScore = examStudentOptionEntity.getScore();
                                if(!NumberUtils.DOUBLE_MINUS_ONE.equals(stuTopicScore)){
                                    //客观题
                                    if(TestTopicTypeEnum.SINGLECHOICE.getSqlValue().equals(e.getType())
                                            || TestTopicTypeEnum.MULTIPLECHOICE.getSqlValue().equals(e.getType())
                                            || TestTopicTypeEnum.JUDGETOPIC.getSqlValue().equals(e.getType())){
                                        stuObjectScore.getAndAdd(stuTopicScore);
                                    }else{
                                        stuSubjectScore.getAndAdd(stuTopicScore);
                                    }
                                }
                                examTopicListRes.setStuScore(stuTopicScore);
                                Integer checkStatus = examStudentOptionEntity.getCheckStatus();
                                examTopicListRes.setCheckStatus(checkStatus);
                                //题目分数
                                Double topicScore = e.getScore().doubleValue();
                                //默认设置未批
                                examTopicListRes.setStuMark(ExamStuMarkEnum.NO_CORRECT.getValue());
                                //设置题卡 得分情况
                                if(checkStatus == 1){
                                    if(stuTopicScore.equals(topicScore)){
                                        examTopicListRes.setStuMark(ExamStuMarkEnum.FULL_SCORE.getValue());
                                    }else if(stuTopicScore > 0){
                                        examTopicListRes.setStuMark(ExamStuMarkEnum.PART_SCORE.getValue());
                                    }else if(NumberUtils.DOUBLE_ZERO.equals(stuTopicScore)){
                                        examTopicListRes.setStuMark(ExamStuMarkEnum.ZERO_SCORE.getValue());
                                    }
                                }
                            }
                            examTopicListRes.setAnswerId(examStudentOptionEntity.getId());
                        }
                        if (MapUtils.isNotEmpty(finalTopicOptionMap)) {
                            //匹配试题id
                            List<ExamTopicOptionEntity> examTopicOptionEntities = finalTopicOptionMap.get(e.getId());
                            if (CollectionUtils.isNotEmpty(examTopicOptionEntities)) {
                                //设置选项列表
                                String finalAnswer = answer;
                                examTopicListRes.setOptions(getTopicOptionList(examTopicOptionEntities, finalAnswer));
                            }
                        }
                        //重新设置排序，从1开始
                        examTopicListRes.setSort(atomicInteger.getAndAdd(1));
                        return examTopicListRes;
                    })
                    .collect(Collectors.toList());
        }
        //仅看未批
        if(noCorrected == 1 && CollectionUtils.isNotEmpty(examTopicListResList)){
            examTopicListResList = examTopicListResList.stream().filter(e -> NumberUtils.DOUBLE_MINUS_ONE.equals(e.getStuScore())).collect(Collectors.toList());
        }

        // 设置知识点
        List<ExamCheckTopicInfoRes> finalExamTopicListResList = examTopicListResList;
        List<TopicKnowledgePvdRes> topicKnowledgeByTopicIds = topicKnowledgeIntegration.getTopicKnowledgeByTopicIds(finalExamTopicListResList.stream().map(ExamCheckTopicInfoRes::getId).collect(Collectors.toList()));
        // key -> 题目id， valud -> 知识点集合
        Map<Long, List<TopicKnowledgePvdRes>> topicKnowledgeMap = topicKnowledgeByTopicIds.parallelStream().collect(Collectors.groupingBy(TopicKnowledgePvdRes::getTopicId));
        examTopicListResList.forEach(e -> {
            e.setTopicKnowledgePvdRes(topicKnowledgeMap.get(e.getId()));
        });

        // 批改详情增加评语 v230
        // 考试学生答题卡id
        Long examStuAnswerId = examStudentAnswerEntity.getId();
        StuRecordRemarkEntity entity = stuRecordRemarkDao.selectByRecordId(examStuAnswerId);
        Long remarkId = NumberUtils.LONG_ZERO;
        String remark = "";
        if (Optional.ofNullable(entity).isPresent()) {
            remark = entity.getRemark();
            remarkId = entity.getId();
        }

        return ExamCheckInfoRes.builder()
                .remarkId(remarkId)
                .remark(remark)
                .submitTime(examStudentAnswerEntity.getSubmitTime())
                .avatar(avatar)
                .name(userName)
                .no(no)
                .score(score)
                .examAnswerId(id)
                .examId(examActivityEntity.getId())
                .stuScore(stuScore)
                .examName(examActivityEntity.getName())
                .stuObjectScore(stuObjectScore.get())
                .stuSubjectScore(stuSubjectScore.get())
                .objectScore(objectScore)
                .subjectScore(subjectScore)
                .screenLimit(examStudentAnswerEntity.getScreenLimit())
                .screenTime(examStudentAnswerEntity.getScreenTime())
                .examCheckTopicInfoResList(examTopicListResList)
                .build();
    }

    /**
     * v230 - 导出学生考试成绩PDF数据
     * @param examPaperId 考试试卷id
     * @param stuExamId 学生考试答题卡id
     * @return {@link List<StuTestPDFRes>}
     */
    @Override
    public List<StuExamPDFRes> exportStuExamPDF(Long examPaperId, Long stuExamId) {
        List<Long> stuExamIds = new ArrayList<>();
        // 导出所有已批改PDF数据
        if (NumberUtils.LONG_ZERO.equals(stuExamId)) {
            // 1.根据考试试卷id查询所有已批学生的答题卡ids
            stuExamIds = examStudentAnswerDao.getSubmittedCheckedIds(examPaperId);
            if (CollectionUtils.isEmpty(stuExamIds)) {
                return new ArrayList<>();
            }
        }
        // 导出单个PDF数据
        else {
            stuExamIds.add(stuExamId);
        }
        // 根据所有答题卡ids查询数据
        return getStuExamResult(examPaperId, stuExamIds);
    }

    /**
     * 老师-批阅试卷-批改试卷
     *
     * @param examCheckSubjectiveReq
     * @return {@link ExamOrTestCheckSubjectiveRes}
     * <AUTHOR>
     * @date 2020/12/3 9:18
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ExamOrTestCheckSubjectiveRes checkQuizTaskDetail1(ExamCheckSubjectiveReq examCheckSubjectiveReq) {
        //1.根据答题卡id获取答题卡详情
        Long answerId = examCheckSubjectiveReq.getAnswerId();
        ExamStudentAnswerEntity examStudentAnswerEntity = examStudentAnswerDao.selectById(answerId);
        if (examStudentAnswerEntity == null) {
            throw ExamActivityError.NO_EXIST_ANSWER.error();
        }
        //2.判定权限
        Long memberId = TenantMemberHolder.get().getMemberId();
        examMemberRoleService.checkPermission(examStudentAnswerEntity.getExamId(), ExamMemberRoleEnum.CHECK.getValue(), ExamModuleEnum.CORRECT.getValue());
        Long answerTopicId = examCheckSubjectiveReq.getAnswerTopicId();

        //3.获取答题卡试题答案
        ExamStudentOptionEntity examStudentOptionEntity = examStudentOptionDao.selectById(answerTopicId);
        if (examStudentOptionEntity == null) {
            throw BaseCmmError.RECORD_NOT_EXIST.error("试题答案");
        }
        //分数为-1时，代表清空分数
        examStudentOptionEntity.setCheckStatus(NumberUtils.DOUBLE_MINUS_ONE.equals(examCheckSubjectiveReq.getScore()) ? YesNoEnum.NO.getValue() : YesNoEnum.YES.getValue());
        examStudentOptionEntity.setScore(examCheckSubjectiveReq.getScore());
        examStudentOptionDao.updateById(examStudentOptionEntity);

        //3.计算总分
        List<ExamStudentOptionEntity> examStudentOptionEntityList = examStudentOptionDao.getListByExamIdAndMemberId(examStudentAnswerEntity.getMemberId(), examStudentAnswerEntity.getExamId(), answerId);
        Double score = NumberUtils.DOUBLE_ZERO;
        //学生客观题总分
        AtomicDouble stuObjectScore = new AtomicDouble(NumberUtils.DOUBLE_ZERO);
        //学生主观题总分
        AtomicDouble stuSubjectScore = new AtomicDouble(NumberUtils.DOUBLE_ZERO);
        if (CollectionUtils.isNotEmpty(examStudentOptionEntityList)) {
            score = examStudentOptionEntityList.stream().filter(e -> !NumberUtils.DOUBLE_MINUS_ONE.equals(e.getScore())).mapToDouble(ExamStudentOptionEntity::getScore).sum();
            examStudentAnswerEntity.setScore(score);
            List<ExamTopicEntity> topicEntities = examTopicDao.getListByExamId(examStudentAnswerEntity.getExamId());
            topicEntities.forEach(e -> {
                ExamStudentOptionEntity optionEntity = examStudentOptionEntityList.stream().filter(t -> t.getTopicId().equals(e.getId())).findAny().orElse(null);
                //学生-题目得分
                if (optionEntity != null && optionEntity.getScore() != null) {
                    Double stuTopicScore = optionEntity.getScore();
                    if(!NumberUtils.DOUBLE_MINUS_ONE.equals(stuTopicScore)) {
                        //客观题
                        if (TestTopicTypeEnum.SINGLECHOICE.getSqlValue().equals(e.getType())
                                || TestTopicTypeEnum.MULTIPLECHOICE.getSqlValue().equals(e.getType())
                                || TestTopicTypeEnum.JUDGETOPIC.getSqlValue().equals(e.getType())) {
                            stuObjectScore.getAndAdd(stuTopicScore);
                        } else {
                            stuSubjectScore.getAndAdd(stuTopicScore);
                        }
                    }
                }
            });


            //4.设置批改信息,当所有题目都批改了，才设置批改完成
            List<ExamStudentOptionEntity> correctedTopicList = examStudentOptionEntityList.stream()
                    .filter(e -> YesNoEnum.YES.getValue().equals(e.getCheckStatus()))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(correctedTopicList) && examStudentOptionEntityList.size() == correctedTopicList.size()){
                // 只有第一次 从 0 变成 1 时发送mq消息
                if(examStudentAnswerEntity.getCheckStatus().equals(NumberUtils.INTEGER_ZERO)){
                    // 根据成员id 获取uid信息
                    Long stuMemberId = examStudentAnswerEntity.getMemberId();
                    TenantMemberPvdRes tenantMemberPvdRes = memberIntegration.getByMemberId(stuMemberId);
                    // 所有题目都批改之后，设置批改完成
                    CompletableFuture.runAsync(() ->
                            sendCalculateMsgUtil.sendCalculateMsgV1(CalculateGrowthMsg.builder()
                                    .dataId(examStudentAnswerEntity.getExamId())
                                    .memberId(stuMemberId)
                                    .uid(tenantMemberPvdRes.getUid())
                                    .scoreDataTypeEnum(CalculateStuScoreDataTypeEnum.EXAM)
                                    .tenantId(examStudentAnswerEntity.getTenantId())
                                    // 增加当前时间戳
                                    .messageTime(DateHelper.instance().currentMills())
                                    .build())
                    );
                }
                examStudentAnswerEntity.setCheckStatus(NumberUtils.INTEGER_ONE);
                examStudentAnswerEntity.setCheckTime(DateHelper.instance().currentMills());
                examStudentAnswerEntity.setCheckUserId(memberId);

            }else{
                examStudentAnswerEntity.setCheckStatus(NumberUtils.INTEGER_ZERO);
            }
        }
        examStudentAnswerDao.updateById(examStudentAnswerEntity);
        return ExamOrTestCheckSubjectiveRes.builder()
                .stuObjectScore(stuObjectScore.get())
                .stuSubjectScore(stuSubjectScore.get())
                .score(score)
                .build();
    }

    /**
     * 老师-批阅试卷-详情列表
     *
     * @param examId 考试id
     * @param name   姓名或学号搜索
     * @param status 0：全部,1:未提交,2：未批改,3：已批改
     * @param sortRule 排序正序：得分正序/倒序
     * @return {@link ExamCheckDetailListRes}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExamCheckDetailListRes checkQuizTaskList(Long examId, String name, Integer status, String sortRule) {
        //获取考试详情
        ExamActivityEntity examActivityEntities = examActivityDao.selectById(examId);
        if (!Optional.ofNullable(examActivityEntities).isPresent()) {
            throw CommonError.RECORD_NOT_EXIST.error("考试");
        }
        Integer publishStatus = examActivityEntities.getPublishStatus();
        Long tenantId = examActivityEntities.getTenantId();
        List<Long> validMemberIds = this.tenantIntegration.getValidMemberIds(tenantId);
        if (CollectionUtils.isEmpty(validMemberIds)) {
            return ExamCheckDetailListRes
                    .builder()
                    .name(examActivityEntities.getName())
                    .insertTime(examActivityEntities.getInsertTime())
                    .publishStatus(publishStatus)
                    .build();
        }
        //1.根据考试id获取 考试成员列表
        List<ExamActivityStudentEntity> examActivityStudentEntitys = examActivityStudentDao.selByExamIdAndMemberIds(examId, validMemberIds);
        if (CollectionUtils.isEmpty(examActivityStudentEntitys)) {
            return ExamCheckDetailListRes
                    .builder()
                    .name(examActivityEntities.getName())
                    .insertTime(examActivityEntities.getInsertTime())
                    .publishStatus(publishStatus)
                    .build();
        }
        //总人数
        Integer allCount = examActivityStudentEntitys.size();
        //计算提交人数
        int submittedCnt = examStudentAnswerDao.getSubmittedCntByExamIdAndMemberIds(examId, validMemberIds);
        //获取memberIds
        List<Long> memberIds = examActivityStudentEntitys.stream().map(ExamActivityStudentEntity::getMemberId).collect(Collectors.toList());
        //根据memberIds获取用户信息
        List<TenantMemberPvdRes> tenantMemberPvdRes = tenantMemberIntegration.listByMemberIds(memberIds);
        List<TenantMemberPvdRes> tenantMemberPvdResList = tenantMemberPvdRes;
        //过滤条件 姓名或学号搜索
        if (StringUtils.isNotEmpty(name)) {
            tenantMemberPvdResList = tenantMemberPvdRes.stream()
                    .filter(e -> e.getName().contains(name) || e.getNo().contains(name))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(tenantMemberPvdResList)) {
            return ExamCheckDetailListRes.builder()
                    .allCount(allCount)
                    .submitCount(submittedCnt)
                    .name(examActivityEntities.getName())
                    .insertTime(examActivityEntities.getInsertTime())
                    .publishStatus(publishStatus)
                    .build();
        }
        List<Long> userIds = tenantMemberPvdResList.stream().map(TenantMemberPvdRes::getId).collect(Collectors.toList());
        //2.根据考试id、过滤之后的学生成员ids 获取学生答题卡列表
        List<ExamCheckAnswerListRes> examCheckAnswerListRes = getCheckAnswerList(examId, userIds, tenantMemberPvdResList, status);

        examCheckAnswerListRes = examCheckAnswerListRes.parallelStream().peek(o -> {
            if (ExamStatusEnum.PROCESS.getValue().equals(publishStatus)) {
                o.setResitFlag(NumberUtils.INTEGER_MINUS_ONE);
            }
        }).collect(Collectors.toList());

        Comparator<ExamCheckAnswerListRes> comparator;
        if (StringUtils.isBlank(sortRule)) {
            // 默认按提交时间降序排序
            comparator = Comparator.comparing(ExamCheckAnswerListRes::getSubmitTime).reversed();
        } else {
            // 得分倒序
            if ("stu_score_desc".equals(sortRule)) {
                comparator = Comparator.comparing(ExamCheckAnswerListRes::getScore).reversed();
            } else {
                // 得分正序
                comparator = Comparator.comparing(ExamCheckAnswerListRes::getScore);
            }
        }

        examCheckAnswerListRes = examCheckAnswerListRes.stream()
                .sorted(comparator)
                .collect(Collectors.toList());

        return ExamCheckDetailListRes.builder()
                .allCount(allCount)
                .submitCount(submittedCnt)
                .name(examActivityEntities.getName())
                .insertTime(examActivityEntities.getInsertTime())
                .publishStatus(publishStatus)
                .examCheckAnswerListRes(examCheckAnswerListRes)
                .examId(examId)
                .endTime(examActivityEntities.getStartMills() + examActivityEntities.getTimeLimit())
                .build();
    }

    /**
     * 获取学生答题卡列表
     *
     * @param examId                 考试id
     * @param userIds                用户ids
     * @param tenantMemberPvdResList 租户成员集合
     * @param status 0：全部,1:未提交,2：未批改,3：已批改
     * @return {@link List< ExamCheckAnswerListRes>}
     * <AUTHOR>
     * @date 2020/12/3 16:13
     */
    private List<ExamCheckAnswerListRes> getCheckAnswerList(Long examId, List<Long> userIds, List<TenantMemberPvdRes> tenantMemberPvdResList, Integer status) {
        //客观题数量
        int objCnt = examTopicDao.getCntTopicByTypes(examId, TestTopicTypeEnum.getObjTopicTypes());
        //主观题数量
        int subCnt = examTopicDao.getCntTopicByTypes(examId, TestTopicTypeEnum.getSubTopicTypes());

        // 考试实体
        ExamActivityEntity examEntity = examActivityDao.selectById(examId);
        // 考试考试时间
        Long startMills = examEntity.getStartMills();
        // 是否允许补足迟到时间
        Integer fillTimeStatus = examEntity.getFillTimeStatus();
        // 考试时长
        Long timeLimit = examEntity.getTimeLimit() * 60 * 1000L;

        // 学生答题卡列表
        List<ExamStudentAnswerEntity> examStudentAnswerEntities = examStudentAnswerDao.getListByExamId(examId);


        if(CollectionUtils.isNotEmpty(examStudentAnswerEntities)){
            // 题目ids
            List<Long> answerIds = examStudentAnswerEntities.parallelStream()
                    .map(ExamStudentAnswerEntity::getId)
                    .collect(Collectors.toList());

            //3. 获取学生试题答案列表
            List<ExamStudentOptionEntity> examStudentOptionList = examStudentOptionDao.getListByStuAnswerIds(answerIds);
            // key -> answerId, value -> pair
            Map<Long, Pair<Boolean, Double>> answerScoreMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(examStudentOptionList)){
                // 对answerId进行分组
                examStudentOptionList.parallelStream()
                        .collect(Collectors.groupingBy(ExamStudentOptionEntity::getAnswerId))
                        .forEach((key, stuExamOptionList) -> {
                            // 设置批改信息,当所有题目都批改了，才设置批改完成
                            List<ExamStudentOptionEntity> correctedTopicList = stuExamOptionList.stream().filter(e -> YesNoEnum.YES.getValue().equals(e.getCheckStatus())).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(correctedTopicList) && stuExamOptionList.size() == correctedTopicList.size()) {
                                double sum = correctedTopicList.parallelStream()
                                        .filter(e -> !NumberUtils.DOUBLE_MINUS_ONE.equals(e.getScore()))
                                        .mapToDouble(ExamStudentOptionEntity::getScore)
                                        .sum();
                                answerScoreMap.put(key, Pair.of(Boolean.TRUE, sum));
                            }
                        });
            }

            //组装数据
            examStudentAnswerEntities = examStudentAnswerEntities.stream()
                    .peek(examStudentAnswerEntity -> {
                        Pair<Boolean, Double> pair = answerScoreMap.getOrDefault(examStudentAnswerEntity.getId(), Pair.of(Boolean.FALSE, NumberUtils.DOUBLE_ZERO));
                        //设置批改信息,当所有题目都批改了，才设置批改完成
                        if(pair.getLeft()){
                            examStudentAnswerEntity.setScore(pair.getRight());
                            examStudentAnswerEntity.setCheckStatus(NumberUtils.INTEGER_ONE);
                            examStudentAnswerEntity.setCheckTime(DateHelper.instance().currentMills());
                            examStudentAnswerEntity.setCheckUserId(NumberUtils.LONG_ZERO);
                        }
                    })
                    .collect(Collectors.toList());

            //批量更新
            examStudentAnswerDao.updateBatchExamStatus(examStudentAnswerEntities);
        }


        // 学生迟到时间map key -> 学生答题卡id， value -> 该考生的考试结束时间（1、允许补足迟到时间：进入考试时间 + 考试时长 2、不允许补足迟到时间：进入考试时间 + 考试时长 - 迟到时间）
        Map<Long, Long> examStuLateMillsMap = examStudentAnswerEntities.parallelStream().collect(Collectors.toMap(new Function<ExamStudentAnswerEntity, Long>() {
            @Override
            public Long apply(ExamStudentAnswerEntity entity) {
                return entity.getId();
            }
        }, new Function<ExamStudentAnswerEntity, Long>() {
            @Override
            public Long apply(ExamStudentAnswerEntity entity) {
                // 考生进入考试时间
                Long signTime = entity.getSignTime();

                // 如果是补考
                if (Objects.equals(NumberUtils.INTEGER_ONE, entity.getResitFlag())) {
                    if (signTime > NumberUtils.INTEGER_ZERO) {
                        return signTime + timeLimit;
                    }
                    return NumberUtils.LONG_ZERO;
                }

                // 如果允许补足迟到时间，考生考试结束时间为进入考试的时间 + 考试时长
                if (Objects.equals(NumberUtils.INTEGER_ONE, fillTimeStatus)) {
                    return signTime + timeLimit;
                }
                // 迟到的毫秒数
                long lateMills = signTime - startMills;
                // 如果不允许补足迟到时间
                return signTime + timeLimit - lateMills;
            }
        }));
        if (NumberUtils.INTEGER_ONE.equals(status)) {

            if (CollectionUtils.isNotEmpty(examStudentAnswerEntities)) {
                List<Long> answerIds = examStudentAnswerEntities.stream().map(ExamStudentAnswerEntity::getMemberId).collect(Collectors.toList());
                List<ExamStudentAnswerEntity> finalExamStudentAnswerEntityList = examStudentAnswerEntities;
                List<Long> finalAnswerIds = answerIds;
                List<ExamCheckAnswerListRes> examCheckAnswerListResList = tenantMemberPvdResList.stream()
                        .filter(t -> CollectionUtils.isEmpty(finalAnswerIds) || !finalAnswerIds.contains(t.getId()))
                        .map(e -> {
                            return getExamCheckAnswer(finalExamStudentAnswerEntityList, e, objCnt, subCnt, examStuLateMillsMap);
                        })
                        .collect(Collectors.toList());
                List<ExamStudentAnswerEntity> noSubmitAndParted = examStudentAnswerEntities.stream()
                        .filter(e -> NumberUtils.INTEGER_ZERO.equals(e.getSubmitStatus()))
                        .collect(Collectors.toList());
                List<ExamCheckAnswerListRes> checkAnswerListResList = new ArrayList<>(NumberUtils.INTEGER_ONE);
                if (CollectionUtils.isNotEmpty(noSubmitAndParted)) {
                    checkAnswerListResList = noSubmitAndParted.stream()
                            .map(e -> {
                                return getExamCheckAnswer(tenantMemberPvdResList, e, objCnt, subCnt, examStuLateMillsMap);
                            })
                            .collect(Collectors.toList());
                }
                checkAnswerListResList.addAll(examCheckAnswerListResList);
                return checkAnswerListResList;
            } else {
                //查询全部
                return tenantMemberPvdResList.stream()
                        .map(e -> {
                            return getExamCheckAnswer(null, e, objCnt, subCnt, examStuLateMillsMap);
                        })
                        .collect(Collectors.toList());
            }

        }
        //学生
        List<ExamStudentAnswerEntity> examStudentAnswerEntities1 = examStudentAnswerDao.getListByExamIdAndUserIds(examId, userIds, status);
        List<ExamStudentAnswerEntity> finalExamStudentAnswerEntities = examStudentAnswerEntities1;
        if (NumberUtils.INTEGER_ZERO.equals(status)) {
            //查询全部
            return tenantMemberPvdResList.stream()
                    .map(e -> {
                        return getExamCheckAnswer(finalExamStudentAnswerEntities, e, objCnt, subCnt, examStuLateMillsMap);
                    })
                    .collect(Collectors.toList());
        }
        //已提交未批改、或已批改
        return finalExamStudentAnswerEntities.stream()
                .map(e -> {
                    return getExamCheckAnswer(tenantMemberPvdResList, e, objCnt, subCnt, examStuLateMillsMap);
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取考试答题卡
     *
     * @param tenantMemberPvdResList 成员对象
     * @param e                      学生答题卡
     * @param objCnt                 主管题数量
     * @param subCnt                 客观题数量
     * @return {@link ExamCheckAnswerListRes}
     * <AUTHOR>
     * @date 2020/12/9 15:51
     */
    private ExamCheckAnswerListRes getExamCheckAnswer(List<TenantMemberPvdRes> tenantMemberPvdResList, ExamStudentAnswerEntity e, int objCnt, int subCnt, Map<Long, Long> examStuLateMillsMap) {
        TenantMemberPvdRes tenantMemberPvdRes = tenantMemberPvdResList.stream().filter(t -> t.getId().equals(e.getMemberId())).findAny().orElse(null);
        if (tenantMemberPvdRes == null) {
            return null;
        }
        return getExamCheckAnswerListRes(e, objCnt, subCnt, tenantMemberPvdRes, examStuLateMillsMap);
    }

    /**
     * 获取考试答题卡
     *
     * @param examStudentAnswerEntities 答题卡列表
     * @param e                         成员
     * @param objCnt                    主管题数量
     * @param subCnt                    客观题数量
     * @return {@link ExamCheckAnswerListRes}
     * <AUTHOR>
     * @date 2020/12/9 15:27
     */
    private ExamCheckAnswerListRes getExamCheckAnswer(List<ExamStudentAnswerEntity> examStudentAnswerEntities, TenantMemberPvdRes e, int objCnt, int subCnt, Map<Long, Long> examStuLateMillsMap) {
        if (CollectionUtils.isNotEmpty(examStudentAnswerEntities)) {
            ExamStudentAnswerEntity examStudentAnswerEntity = examStudentAnswerEntities.stream().filter(t -> e.getId().equals(t.getMemberId())).findAny().orElse(null);

            if (examStudentAnswerEntity == null) {
                return ExamCheckAnswerListRes.builder()
                        .countObj(objCnt)
                        .countSubj(subCnt)
                        //“未交_0"
                        .status(NumberUtils.INTEGER_ZERO)
                        .screenLimit(NumberUtils.INTEGER_ZERO)
                        .screenTime(NumberUtils.LONG_ZERO)
                        .submitTime(NumberUtils.LONG_ZERO)
                        .endTime(NumberUtils.LONG_ZERO)
                        .memberId(e.getId())
                        .no(e.getNo())
                        .name(e.getName())
                        .endTime(NumberUtils.LONG_ZERO)
                        .resitFlag(NumberUtils.INTEGER_ZERO)
                        .firstEnterTime(NumberUtils.LONG_ZERO)
                        .build();
            }
            return getExamCheckAnswerListRes(examStudentAnswerEntity, objCnt, subCnt, e, examStuLateMillsMap);
        }
        return ExamCheckAnswerListRes.builder()
                .countObj(objCnt)
                .countSubj(subCnt)
                //“未交_0"
                .status(NumberUtils.INTEGER_ZERO)
                .screenLimit(NumberUtils.INTEGER_ZERO)
                .screenTime(NumberUtils.LONG_ZERO)
                .submitTime(NumberUtils.LONG_ZERO)
                .resitFlag(NumberUtils.INTEGER_ZERO)
                .endTime(NumberUtils.LONG_ZERO)
                .memberId(e.getId())
                .no(e.getNo())
                .name(e.getName())
                .firstEnterTime(NumberUtils.LONG_ZERO)
                .build();
    }

    /**
     * 组装数据
     *
     * @param e                  答题卡
     * @param objCnt             主观题数量
     * @param subCnt             客观题数量
     * @param tenantMemberPvdRes 成员
     * @return {@link ExamCheckAnswerListRes}
     * <AUTHOR>
     * @date 2020/12/9 15:58
     */
    private ExamCheckAnswerListRes getExamCheckAnswerListRes(ExamStudentAnswerEntity e, int objCnt, int subCnt, TenantMemberPvdRes tenantMemberPvdRes, Map<Long, Long> examStuLateMillsMap) {
        Long screenTime = e.getScreenTime();

        Long m = screenTime / 60000L;

        Long s = (screenTime % 60000L) / 1000L;

        // 考生本次考试结束时间
        Long endTime = examStuLateMillsMap.getOrDefault(e.getId(), NumberUtils.LONG_ZERO);

        boolean flag = tenantMemberPvdRes.getExperienceDuration() > 0;
        Long goBackSignTime = e.getGoBackSignTime() == null ? NumberUtils.LONG_ZERO : e.getGoBackSignTime();
        ExamCheckAnswerListRes build = ExamCheckAnswerListRes.builder()
                .countObj(objCnt)
                .countSubj(subCnt)
                .id(e.getId())
                .score(NumberUtils.INTEGER_ZERO.equals(e.getCheckStatus()) ? null : e.getScore())
                //“未交_0","未批_1","已批_2”
                .status(NumberUtils.INTEGER_ZERO.equals(e.getSubmitStatus())
                        ? NumberUtils.INTEGER_ZERO : NumberUtils.INTEGER_ZERO.equals(e.getCheckStatus())
                        ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_TWO)
                .submitTime(NumberUtils.LONG_ZERO.equals(e.getSubmitTime()) ? 0 : e.getSubmitTime())
                .submitDate(NumberUtils.LONG_ZERO.equals(e.getSubmitTime()) ? null : new Date(e.getSubmitTime()))
                .screenLimit(e.getScreenLimit())
                .screenTime(screenTime)
                .memberId(e.getMemberId())
                .no(flag ? "体验学员" : tenantMemberPvdRes.getNo())
                .name(tenantMemberPvdRes.getName())
                .expStuFlag(flag ? 1 : 0)
                .endTime(endTime)
                .resitFlag(e.getResitFlag())
                .goBackFlag(e.getGoBackFlag())
                .firstEnterTime(goBackSignTime > NumberUtils.LONG_ZERO ? goBackSignTime : e.getSignTime())
                .firstEnterDate(goBackSignTime > NumberUtils.LONG_ZERO ? new Date(goBackSignTime) : new Date(e.getSignTime()))
                .build();
        if (screenTime > NumberUtils.LONG_ZERO) {
            build.setScreenTimeStr(String.format("%s分%s秒", m, s));
        }
        return build;
    }


    /**
     * 老师-批阅试卷-导出学生成绩
     *
     * @param examId 考试id
     * @param name   姓名或学号搜索
     * @param status 0：全部学生,1：已交,2：未交
     * @return {@link Workbook }
     * <AUTHOR>
     * @date 2020/12/3 11:17
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Workbook exportCheckQuizTaskList(Long examId, String name, Integer status) {
        //1.根据考试id获取 考试成员列表
        List<ExamActivityStudentEntity> examActivityStudentEntitys = examActivityStudentDao.selByExamId(examId);
        if (CollectionUtils.isEmpty(examActivityStudentEntitys)) {
            throw ExamActivityError.NO_MEMBER_EXAM.error();
        }
        //获取memberIds
        List<Long> memberIds = examActivityStudentEntitys.stream().map(ExamActivityStudentEntity::getMemberId).collect(Collectors.toList());
        //根据memberIds获取用户信息
        List<TenantMemberPvdRes> tenantMemberPvdResList = tenantMemberIntegration.listByMemberIds(memberIds);
        //2.根据考试id、过滤之后的学生成员ids 获取学生答题卡列表
        List<ExamCheckAnswerListRes> examCheckAnswerListRes = getCheckAnswerList(examId, memberIds, tenantMemberPvdResList, NumberUtils.INTEGER_ZERO);
        //3.导出
        ExportParams exportParams = new ExportParams();
        exportParams.setType(ExcelType.XSSF);
        exportParams.setStyle(SheetExcelExportStyler.class);
        return ExcelExportUtil.exportExcel(exportParams, ExamCheckAnswerListRes.class, examCheckAnswerListRes);
    }


    /**
     * 保存试卷  返回对用判定条件（分区id、权限组、是否移动删除）
     *
     * @param id
     * @return
     * <AUTHOR>
     * @date 2020/12/4 09:53
     */
    @Override
    public ExamSaveBtnRes checkSaveBtn(Long id) {
        ExamActivityEntity examActivityEntity = examActivityDao.selectById(id);
        Long partitionId = NumberUtils.LONG_ZERO;
        Integer permissionType = PermissionGroupTypeEnum.NO_PERM.getValue();
        Integer status = YesNoEnum.NO.getValue();
        Long memberId = TenantMemberHolder.get().getMemberId();
        if (!examActivityEntity.getOriPartitionId().equals(partitionId)) {
            //分区id不是0
            partitionId = examActivityEntity.getOriPartitionId();
            PartitionPvdRes partitionPvdRes = partitionIntegration.partitionInfo(partitionId);
            List<PermissionGroupEntity> permissionGroupEntities = permissionGroupDao.findByTypePocId(PartitionTypeEnum.QUIZ_PAPER.getName(), partitionId, TenantMemberHolder.get().getTenantId());
            PermissionGroupEntity permissionGroupEntity = permissionGroupEntities.stream().filter(e -> e.getAuthId().equals(memberId)).findAny().orElse(null);
            if (permissionGroupEntity == null) {
                //不在授权组 编辑组
                if (partitionPvdRes.getMid().equals(memberId)) {
                    //是分区持有人
                    permissionType = PermissionGroupTypeEnum.OWN.getValue();
                }
            } else {
                //在授权组编辑组
                permissionType = permissionGroupEntity.getPermissionType();
            }
            Long quizPaperId = examActivityEntity.getOriPaperId();
            if (quizPaperId != NumberUtils.LONG_ZERO) {
                //有对应的旧的试卷
                QuizPaperPvdRes quizPaperPvdRes = quizPaperIntegration.paperById(quizPaperId);
                if (quizPaperPvdRes != null && partitionId.equals(quizPaperPvdRes.getPartitionId())) {
                    //有试卷信息  且试卷还在原来分区
                    status = YesNoEnum.YES.getValue();
                }
            }
        }
        return ExamSaveBtnRes.builder()
                .partitionId(partitionId)
                .permissionType(permissionType)
                .status(status)
                .build();
    }

    /**
     * 保存为新试卷
     *
     * @param examId
     * @param partitionId
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisDistributedLock(keys = {
            "T(java.lang.String).format(T(com.ioie.tac.web.constant.Constants).LOCK_UPDATE_TOPICKNOWLEDGE_KNOWLEDGEID, #examId)"})
    public void saveNew(Long examId, Long partitionId, ExamActivityReq examActivityReq) {
        //试卷信息
        ExamActivityEntity examActivityEntity = examActivityDao.selectById(examId);
        if (examActivityEntity == null) {
            //抛异常 数据不存在
            throw BaseCmmError.RECORD_NOT_EXIST.error();
        }
        //初始化旧的考卷id
        Long oldQuizPaperId = null;
        if (partitionId == null) {
            //覆盖旧试卷  分区id取自己
            partitionId = examActivityEntity.getOriPartitionId();
            oldQuizPaperId = examActivityEntity.getOriPaperId();
        }
        List<QuizTopicPvdReq> topics = new ArrayList<>();
        QuizPaperPvdReq quizPaperPvdReq = QuizPaperPvdReq.builder()
                .examId(examActivityEntity.getId())
                .id(oldQuizPaperId)
                .name(examActivityReq.getName())
                .introduction(examActivityReq.getIntroduction())
                .partitionId(partitionId)
                .build();

        //题目列表
        List<ExamTopicEntity> topicEntities = examTopicDao.getListByExamId(examId);
        //选项绑定
        if (CollectionUtils.isNotEmpty(topicEntities)) {
            //查询选项并分组
            Map<Long, List<ExamTopicOptionEntity>> optionGroup = optionsData(topicEntities);
            topics = topicEntities.parallelStream().map(e -> {
                //处理题目信息
                List<QuizTopicOptionPvdReq> options = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(optionGroup.get(e.getId()))) {
                    options = optionGroup.get(e.getId()).parallelStream().map(
                            x -> QuizTopicOptionPvdReq.builder()
                                    .option(x.getOption())
                                    .flag(x.getFlag())
                                    .sort(x.getSort())
                                    .build()
                    ).collect(Collectors.toList());
                }
                return QuizTopicPvdReq.builder()
                        .examTopicId(e.getId())
                        .name(e.getName())
                        .oriTopicId(e.getOriTopicId())
                        .type(e.getType())
                        .createType(e.getCreateType())
                        .answer(e.getAnswer())
                        .analysis(e.getAnalysis())
                        .score(e.getScore())
                        .sort(e.getSort())
                        .level(e.getLevel())
                        .options(options)
                        .complexity(e.getComplexity())
                        .build();
            }).collect(Collectors.toList());

        }

        //组装请求参数录入
        quizPaperPvdReq.setTopics(topics);
        Long quizPaperId = quizPaperIntegration.saveQuizPaperV1(quizPaperPvdReq);
        //进行更新
        examActivityEntity.setOriPaperId(quizPaperId);
        examActivityEntity.setOriPartitionId(partitionId);
        examActivityDao.updateById(examActivityEntity);
    }

    /**
     * 题库导入
     *
     * @param id     试卷id
     * @param idsReq ids
     * @return {@link }
     * <AUTHOR>
     * @date 2020/12/14 10:54
     */
    @Override
    public void importLibrary(Long id, IdsReq idsReq) {
        //排序值处理
        int sort = searchSort(id, examTopicDao);
        //获取kb中的题目ids对应的题目及选项列表集合
        List<TopicLibraryPvdRes> topicLibraryPvdRes = topicLibraryIntegration.listByIds(idsReq.getIds());
        //题目列表为空时抛出异常
        if (CollectionUtils.isEmpty(topicLibraryPvdRes)) {
            throw BaseCmmError.RECORD_NOT_EXIST.error();
        }
        AtomicInteger sortInt = new AtomicInteger(sort);
        List<ExamTopicOptionEntity> examTopicOptionEntities = new ArrayList<>(NumberUtils.INTEGER_ONE);
        List<ExamTopicEntity> examTopicEntityList = topicLibraryPvdRes.stream()
                .map(e -> {
                    long newExamTopicId = ContextHelper.getBean(SnowflakeIdWorker.class).nextId();
                    // 根据题目id查找知识点并绑定新题目
                    List<Long> knowledgeIds = topicKnowledgeIntegration.getKnowledgeIds(e.getOriTopicId());
                    if (!knowledgeIds.isEmpty()) {
                        int i = topicKnowledgeIntegration.insertOne(knowledgeIds, newExamTopicId);
                    }
                    ExamTopicEntity examTopicEntity = ExamTopicEntity.builder()
                            .analysis(e.getAnalysis())
                            .answer(e.getAnswer())
                            .createType(e.getCreateType())
                            .examId(id)
                            .level(e.getLevel() == null ? NumberUtils.INTEGER_ZERO : e.getLevel())
                            .name(e.getName())
                            .oriTopicId(e.getOriTopicId())
                            .score(e.getScore())
                            .sort(sortInt.getAndAdd(NumberUtils.INTEGER_ONE))
                            .type(e.getType())
                            .build();
                    examTopicEntity.setId(newExamTopicId);
                    if (CollectionUtils.isNotEmpty(e.getOptionPvdRes())) {
                        List<ExamTopicOptionEntity> options = e.getOptionPvdRes().stream().map(x ->
                                ExamTopicOptionEntity.builder()
                                        .flag(x.getFlag() == null ? Boolean.TRUE : x.getFlag())
                                        .option(x.getOption())
                                        .sort(x.getSort())
                                        .topicId(newExamTopicId)
                                        .build()
                        ).collect(Collectors.toList());
                        examTopicOptionEntities.addAll(options);
                    }
                    return examTopicEntity;
                })
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(examTopicEntityList)) {
            examTopicDao.batchInsert(examTopicEntityList);
        }
        if (CollectionUtils.isNotEmpty(examTopicOptionEntities)) {
            examTopicOptionDao.batchInsert(examTopicOptionEntities);
        }


    }


    /**
     * 从考卷中选择->获取考卷资源树
     * @param searchName 搜索名称
     * @param targetId
     * <AUTHOR>
     * @date 2021/06/16 12:00
     * @return {@link TopicPackageTreeRes}
     */
    @Override
    public QuizPaperTreeRes quizPaperTree(String searchName, Long targetId) {
        //1.获取我的资料库->考卷集合，加上搜索条件搜索列表
        List<QuizPartitionTreeRes> quizPartitionTreeResList = quizPaperIntegration.getListByName(searchName, targetId);

        //2. 获取学校资料库的数据
        List<PermissionGroupEntity> permissionGroupEntities = permissionGroupDao.getListByAuthId(PartitionTypeEnum.QUIZ_PAPER.getName(), TenantMemberHolder.get().getMemberId(), TenantMemberHolder.get().getTenantId());
        //获取当前用户
        Long authId=TenantMemberHolder.get().getMemberId();
        //当前用户所在租户
        Long tenantId = TenantMemberHolder.get().getTenantId();
        //permission,根据类型及租户id且授权类型为全部类型查询全部授权类型列表
        List<SourcePermissionEntity> sourcePermList = sourcePermissionDao.getListByTypeAndTenantId(PartitionTypeEnum.ACTIVITY.getName(), tenantId);
        List<Long> allPermList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sourcePermList)) {
            List<SourcePermissionEntity> collect = sourcePermList.stream()
                    .filter(e -> !e.getMemberId().equals(authId))
                    .collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(collect)){
                List<Long> dataIds = collect
                        .parallelStream()
                        .map(SourcePermissionEntity::getDataId)
                        .collect(Collectors.toList());
                allPermList.addAll(dataIds);
            }
        }

        if(CollectionUtils.isEmpty(permissionGroupEntities) && CollectionUtils.isEmpty(allPermList) ){
            return QuizPaperTreeRes.builder()
                    .quizTreeList(quizPartitionTreeResList)
                    .build();
        }
        QuizPaperTreeRes quizPaperTreeRes = QuizPaperTreeRes.builder()
                .quizTreeList(quizPartitionTreeResList)
                .build();


        //3.分区ids
//        List<Long> partitionIds = permissionGroupEntities.stream().map(PermissionGroupEntity::getPocid).collect(Collectors.toList());
        List<Long> partitionIds = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(permissionGroupEntities)){
            partitionIds.addAll(permissionGroupEntities.stream().map(PermissionGroupEntity::getPocid).collect(Collectors.toList()));
        }
        if(CollectionUtils.isNotEmpty(allPermList)){
            partitionIds.addAll(allPermList);
        }

        //根据分区ids及搜索名称获取学校资料库->考卷列表
        List<QuizPartitionTreeRes> schoolPackageList = quizPaperIntegration.getSchoolListByPid(partitionIds, searchName, targetId);

        //获取分区创建人列表
        List<Long> memberIds = schoolPackageList.stream().map(QuizPartitionTreeRes::getMemberId).collect(Collectors.toList());
        //租户
        List<TenantMemberPvdRes> tenantMemberPvdResList = tenantMemberIntegration.listByMemberIds(memberIds);
        //按来源用户进行分组，获取分区id kye->成员id，value->分区列表
        Map<Long, List<QuizPartitionTreeRes>> memberQuizPartitionMap = schoolPackageList.stream().collect(Collectors.groupingBy(QuizPartitionTreeRes::getMemberId));

        //查询当前类型 当前用户置顶操作列表
        List<PermissionGroupTopEntity> tops = permissionGroupTopDao.findByTypeOwner(PartitionTypeEnum.QUIZ_PAPER.getName(), TenantMemberHolder.get().getMemberId(), TenantMemberHolder.get().getTenantId());

        //组装数据
        List<QuizPartitionTeaTreeRes> quizPartitionTeaTreeResList = memberQuizPartitionMap.entrySet().stream()
                .map(e -> {
                    QuizPartitionTeaTreeRes quizPartitionTeaTreeRes = new QuizPartitionTeaTreeRes();
                    quizPartitionTeaTreeRes.setMemberId(e.getKey());
                    quizPartitionTeaTreeRes.setQuizPartitionTreeResList(e.getValue());
                    //组装用户信息
                    if(CollectionUtils.isNotEmpty(tenantMemberPvdResList)){
                        Optional<TenantMemberPvdRes> tenantMemberPvdResOptional = tenantMemberPvdResList.stream().filter(tenantMember -> e.getKey().equals(tenantMember.getId())).findAny();
                        tenantMemberPvdResOptional.ifPresent(tenantMemberPvdRes -> quizPartitionTeaTreeRes.setUserName(tenantMemberPvdRes.getName()));
                    }
                    //置顶列表数据绑定top值 topId
                    boolean bool = tops.stream().anyMatch(a -> a.getOwnerId().equals(e.getKey()));
                    if (bool) {
                        quizPartitionTeaTreeRes.setTop(YesNoEnum.YES.getValue());
                    } else {
                        quizPartitionTeaTreeRes.setTop(YesNoEnum.NO.getValue());
                    }
                    return quizPartitionTeaTreeRes;
                })
                .collect(Collectors.toList());

        //result 排序 top 降序  1 置顶  0 不置顶。。。处理
        quizPartitionTeaTreeResList = quizPartitionTeaTreeResList.stream().sorted(Comparator.comparing(QuizPartitionTeaTreeRes::getTop).reversed()).collect(Collectors.toList());

        //重新设值
        quizPaperTreeRes.setSchoolQuizTreeList(quizPartitionTeaTreeResList);

        return quizPaperTreeRes;




    }

    /**
     * 从考卷库中选择->勾选接口
     * @param targetTopicSelectReq 从考卷中导入请求参数
     * <AUTHOR>
     * @date 2021/06/10 12:00
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisDistributedLock(keys = {
            "T(java.lang.String).format(T(com.ioie.tac.web.constant.Constants).LOCK_QUIZ_IMPORT_TARGET, #targetTopicSelectReq.targetId)"})
    public void topicQuizSelected(TargetTopicSelectReq targetTopicSelectReq) {
        //1.kb中插入对应的数据
        topicImportTargetIntegration.addTopicQuizTarget(targetTopicSelectReq);
        //获取题目列表
        List<TopicLibraryPvdRes> topicLibraryPvdRes = quizPaperIntegration.listByIds(targetTopicSelectReq.getTopicIds());
        //判断导入资源类型, 为考试时，需要将答案A,B,C,D转换为id形式
        if(TargetTypeEnum.TEST.getValue().equals(targetTopicSelectReq.getTargetType()) && CollectionUtils.isNotEmpty(topicLibraryPvdRes)){
            topicLibraryPvdRes = topicLibraryPvdRes.stream()
                    .peek(topicLibraryRes -> {
                        String type = topicLibraryRes.getType();
                        //判断类型为单选、多选，处理题目答案
                        if(TestTopicTypeEnum.SINGLECHOICE.getSqlValue().equals(type) || TestTopicTypeEnum.MULTIPLECHOICE.getSqlValue().equals(type)){
                            List<TopicOptionPvdRes> optionPvdRes = topicLibraryRes.getOptionPvdRes();
                            String newAnswer = "";
                            //选项列表不为空，将A,B,C,D答案转换为具体的id
                            if(CollectionUtils.isNotEmpty(optionPvdRes)){
                                for (int i = 0; i < optionPvdRes.size(); i++) {
                                    //为正确选项答案，转化为选项A,B,C,D
                                    if(optionPvdRes.get(i).getFlag()){
                                        newAnswer = newAnswer + optionPvdRes.get(i).getOptionId() + ",";
                                    }
                                }
                            }
                            //重新设置答题
                            if(StringUtils.isNotEmpty(newAnswer)){
                                newAnswer = newAnswer.substring(0, newAnswer.length() -1 );
                            }
                            topicLibraryRes.setAnswer(newAnswer);
                        }
                    })
                    .collect(Collectors.toList());
        }
        //添加选择的资源到目标表中
        addTargetSelectedSource(targetTopicSelectReq, topicImportTargetIntegration, topicLibraryPvdRes, examTopicDao, examTopicOptionDao, topicKnowledgeIntegration);
    }

    /**
     * 添加选择的资源到目标表中
     * @param targetTopicSelectReq 请求参数
     * @param topicImportTargetIntegration 题目导入到目标表中的跨服务（考试，考卷，题库）
     * @param topicLibraryPvdRes 题目列表
     * @param examTopicDao 试题
     * @param examTopicOptionDao 选项
     */
    public static void addTargetSelectedSource(TargetTopicSelectReq targetTopicSelectReq, TopicImportTargetIntegration topicImportTargetIntegration, List<TopicLibraryPvdRes> topicLibraryPvdRes, ExamTopicDao examTopicDao, ExamTopicOptionDao examTopicOptionDao, TopicKnowledgeIntegration topicKnowledgeIntegration){
        targetTopicSelectReq.setTopicLibraryPvdRes(topicLibraryPvdRes);
        //2.根据目标类型，插入不同表的数据  { TargetTypeEnum}
        switch (targetTopicSelectReq.getTargetType()){
            case 0 :
                //考试
                topicImportTargetIntegration.addTestTopic(targetTopicSelectReq);
                break;
            case 1 :
                //考卷
                topicImportTargetIntegration.addQuizTopic(targetTopicSelectReq);
                break;
            case 2 :
                //考试
                // 选项列表接收集合
                List<ExamTopicOptionEntity> topicOptionEntities = new ArrayList<>();
                //获取题目最大排序值
                int sort = searchSort(targetTopicSelectReq.getTargetId(), examTopicDao);
                AtomicInteger atomicInteger = new AtomicInteger(sort);
                //知识点
                List<TopicKnowledgePvdRes> topicKnowledgeByTopicIds = topicKnowledgeIntegration.getTopicKnowledgeByTopicIds(targetTopicSelectReq.getTopicIds());
                List<TopicKnowledgePvdReq> newTopicKnowledgeList = new ArrayList<>();
                //题目列表
                List<ExamTopicEntity> examTopicEntityList = topicLibraryPvdRes.stream()
                        .map(e -> {
                            ExamTopicEntity examTopicEntity = ExamTopicEntity.builder().build();
                            BeanHelper.copyPropertiesIgnoreNull(e, examTopicEntity);
                            Long topicId = ContextHelper.getBean(SnowflakeIdWorker.class).nextId();
                            if (CollectionUtils.isNotEmpty(e.getOptionPvdRes())) {
                                //选项列表不为空时，复制选项信息
                                topicOptionEntities.addAll(e.getOptionPvdRes().stream()
                                        .map(option -> ExamTopicOptionEntity.builder()
                                                .topicId(topicId)
                                                .sort(option.getSort())
                                                .option(option.getOption())
                                                .flag(option.getFlag())
                                                .build()
                                        )
                                        .collect(Collectors.toList()));

                            }

                            List<TopicKnowledgePvdRes> topicKnowledgeEntityList = topicKnowledgeByTopicIds.stream()
                                    .filter(t -> e.getId().equals(t.getTopicId()))
                                    .collect(Collectors.toList());
                            if(CollectionUtils.isNotEmpty(topicKnowledgeEntityList)){
                                //重新设置id, 进行插入
                                newTopicKnowledgeList.addAll(topicKnowledgeEntityList.stream()
                                        .map(t -> TopicKnowledgePvdReq.builder()
                                                    .knowledgeId(t.getId())
                                                    .type(KnowledgeTypeEnum.EXAM.getCode())
                                                    .topicId(topicId)
                                                    .build()
                                        )
                                        .collect(Collectors.toList()));
                            }
                            //设置关联复制后的题目id
                            examTopicEntity.setOriTopicId(e.getId());
                            //设置排序
                            examTopicEntity.setSort(atomicInteger.getAndAdd(1));
                            //设置创建类型
                            examTopicEntity.setCreateType(TopicCreateTypeEnum.IMPORT_TEST.getName());
                            //重新设置考卷id
                            examTopicEntity.setExamId(targetTopicSelectReq.getTargetId());
                            //重新设置id
                            examTopicEntity.setId(topicId);
                            return examTopicEntity;
                        })
                        .collect(Collectors.toList());

                //插入题目列表
                if(CollectionUtils.isNotEmpty(examTopicEntityList)){
                    examTopicDao.batchInsert(examTopicEntityList);
                }
                //插入选项列表
                if(CollectionUtils.isNotEmpty(topicOptionEntities)){
                    examTopicOptionDao.batchInsert(topicOptionEntities);
                }
                //插入知识点
                if(CollectionUtils.isNotEmpty(newTopicKnowledgeList)){
                    topicKnowledgeIntegration.addBatchTopicKnowledge(newTopicKnowledgeList);
                }

                break;
            case 3 :
                // 题库
                topicImportTargetIntegration.addPackageTopic(targetTopicSelectReq);
                break;
            case 4 :
                // 训练
                topicImportTargetIntegration.addPracticeTopic(targetTopicSelectReq);
        }
    }


    /**
     * 从考卷中选择->取消勾选接口
     * @param targetTopicSelectReq 从考卷中导入请求参数
     * <AUTHOR>
     * @date 2021/06/10 12:00
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisDistributedLock(keys = {
            "T(java.lang.String).format(T(com.ioie.tac.web.constant.Constants).LOCK_DEL_QUIZ_IMPORT_TARGET, #targetTopicSelectReq.targetId)"})
    public void topicQuizUnSelected(TargetTopicSelectReq targetTopicSelectReq) {
        //1.删除中间表数据
        topicImportTargetIntegration.deleteTopicQuizTarget(targetTopicSelectReq);
        //2.根据目标类型，删除不同表的数据  { TargetTypeEnum}
        deleteTargetSelectedSource(targetTopicSelectReq, topicImportTargetIntegration, examTopicDao, examTopicOptionDao, topicKnowledgeIntegration);
    }

    /**
     * 删除取消选中的题目
     * @param targetTopicSelectReq 目标取消选中的题目ids等数据
     * @param topicImportTargetIntegration 跨服务
     * @param examTopicDao 考试题目
     * @param examTopicOptionDao 考试题目选项
     */
    public static void deleteTargetSelectedSource(TargetTopicSelectReq targetTopicSelectReq, TopicImportTargetIntegration topicImportTargetIntegration, ExamTopicDao examTopicDao, ExamTopicOptionDao examTopicOptionDao, TopicKnowledgeIntegration topicKnowledgeIntegration) {
        switch (targetTopicSelectReq.getTargetType()){
            case 0 :
                //考试
                topicImportTargetIntegration.delTestTopic(targetTopicSelectReq);
                break;
            case 1 :
                //试卷
                topicImportTargetIntegration.delQuizTopic(targetTopicSelectReq);
                break;
            case 2 :
                //根据考卷id及复制前的题目id查询列表
                List<ExamTopicEntity> examTopicEntityList = examTopicDao.getListByExamIdAndOriTid(targetTopicSelectReq.getTargetId(), targetTopicSelectReq.getTopicIds());
                //获取知识点列表
                List<Long> examTopicIds = examTopicEntityList.stream().map(ExamTopicEntity::getId).collect(Collectors.toList());
                //获取选项列表
                List<ExamTopicOptionEntity> examTopicOptionEntities = examTopicOptionDao.getListByTopicIds(examTopicIds);

                //插入题目列表
                if(CollectionUtils.isNotEmpty(examTopicEntityList)){
                    examTopicDao.deleteBatchIds(examTopicEntityList.stream().map(ExamTopicEntity::getId).collect(Collectors.toList()));
                    //删除知识点
                    List<TopicKnowledgePvdRes> topicKnowledgeByTopicIds = topicKnowledgeIntegration.getTopicKnowledgeByTopicIds(examTopicIds);
                    if(CollectionUtils.isNotEmpty(topicKnowledgeByTopicIds)){
                        topicKnowledgeIntegration.deleteBatchTopicKnowledge(topicKnowledgeByTopicIds.stream()
                                .map(TopicKnowledgePvdRes::getId)
                                .collect(Collectors.toList()));
                    }

                }
                //插入选项列表
                if(CollectionUtils.isNotEmpty(examTopicOptionEntities)){
                    examTopicOptionDao.deleteBatchIds(examTopicOptionEntities.stream().map(ExamTopicOptionEntity::getId).collect(Collectors.toList()));
                }

                break;
            case 3 :
                //题库
                topicImportTargetIntegration.delPackageTopic(targetTopicSelectReq);
                break;
            case 4 :
                // 训练
                topicImportTargetIntegration.delPracticeTopic(targetTopicSelectReq);
                break;
        }
    }

    /**
     * 从考试中选择->获取考试资源列表
     * @description: 考测中心中我管理的+我有命题权限的考试,滚动加载更多（20条）
     * @param searchName 搜索名称
     * @param targetId
     * <AUTHOR>
     * @date 2021/06/16 12:00
     * @return {@link TopicPackageTreeRes}
     */
    @Override
    public BasePageRes<SourceTreeExamListInfoRes> examPaperTree(int pageNo, int pageSize, String searchName, Long targetId) {

        //当前用户id
        Long memberId = TenantMemberHolder.get().getMemberId();
        //1.命题权限的考试列表
        List<ExamMemberRoleEntity> examMemberRoleEntityList = examMemberRoleDao.selExamUpdateRoleList(memberId);
        //考试id
        List<Long> examIds = examMemberRoleEntityList.stream().map(ExamMemberRoleEntity::getExamId).collect(Collectors.toList());

        Page<ExamActivityEntity> page = PageHelper.startPage(pageNo, pageSize);
        List<ExamActivityEntity> examActivityEntityList = examActivityDao.getListByMidAndUpDateExamIds(memberId, examIds, searchName, targetId);
        if(CollectionUtils.isEmpty(examActivityEntityList)){
            return BasePageRes.empty();
        }
        List<SourceTreeExamListInfoRes> examListInfoResList = examActivityEntityList.stream()
                .map(e -> SourceTreeExamListInfoRes.builder()
                        .id(e.getId())
                        .name(e.getName())
                        .insertTime(e.getInsertTime())
                        .build())
                .collect(Collectors.toList());

        return BasePageRes.newBuilder(examListInfoResList)
                .addPageNo(page.getPageNum())
                .addPageSize(page.getPageSize())
                .addTotal(page.getTotal())
                .build();

    }

    /**
     *
     * 从考试库中选择->根据考试id，获取题目列表
     * @param topicListByExamIdReq 请求参数
     * <AUTHOR>
     * @date 2020/11/25 15:21
     * @return {@link BasePageRes<  ExamTopicInfoRes >}
     */
    @Override
    public BasePageRes<ExamTopicInfoRes> topicPageByExamId(Long examId, TopicListByExamIdReq topicListByExamIdReq) {
        // 根据目标id获取题包管理库中的题目列表，
        List<TopicExamTargetEntity> topicExamTargetEntityList = topicExamTargetDao.getListByTargetId(topicListByExamIdReq.getTargetId());
        Page<ExamTopicEntity> page = PageHelper.startPage(topicListByExamIdReq.getPageNo(), topicListByExamIdReq.getPageSize());
        //1.根据搜索条件等，获取题目列表，关联题包管理中已经选过的题目，需要去除已添加到题包管理中的题目
        List<Long> topicIds = topicExamTargetEntityList.stream().map(TopicExamTargetEntity::getTopicId).collect(Collectors.toList());
        List<Long> selectedTopicList = topicListByExamIdReq.getSelectedTopicList();
        if(!CollectionUtils.isEmpty(selectedTopicList) && CollectionUtils.isNotEmpty(topicIds)){
            topicIds = topicIds.stream().filter(e -> !selectedTopicList.contains(e)).collect(Collectors.toList());
        }
        //获取题目列表，且分页, 升序
        List<ExamTopicEntity> examTopicEntityList = examTopicDao.topicListByExamId(examId, topicListByExamIdReq.getType(), topicListByExamIdReq.getKeyword(), topicIds, topicListByExamIdReq.getPracticeFlag());

        // 训练要过滤填空题和简答题
        if (NumberUtils.INTEGER_ONE.equals(topicListByExamIdReq.getPracticeFlag())) {
            examTopicEntityList = examTopicEntityList.stream().filter(o ->
                    !TestTopicTypeEnum.COMPLETION.getSqlValue().equals(o.getType()) &&
                            !TestTopicTypeEnum.SHORTANSWER.getSqlValue().equals(o.getType())).collect(Collectors.toList());
        }

        //2.如果题目为空，返回空
        if(org.springframework.util.CollectionUtils.isEmpty(examTopicEntityList)){
            return BasePageRes.empty();
        }

        return getQuizTopicListResBasePageRes(topicListByExamIdReq.getPageNo(), topicListByExamIdReq.getPageSize(), examTopicEntityList, page, selectedTopicList);

    }

    /**
     * 题目列表
     * @param pageNo 分页
     * @param pageSize 大小
     * @param examTopicEntities 题目列表
     * @param page
     * <AUTHOR>
     * @date 2020/12/24 11:35
     * @return {@link BasePageRes< ExamTopicInfoRes>}
     */
    private BasePageRes<ExamTopicInfoRes> getQuizTopicListResBasePageRes(int pageNo, int pageSize, List<ExamTopicEntity> examTopicEntities, Page<ExamTopicEntity> page, List<Long> selectedTopicList) {
        if(!CollectionUtils.isEmpty(examTopicEntities)){
            List<ExamTopicOptionEntity> topicOptionEntityList = examTopicOptionDao.getListByTopicIds(examTopicEntities.stream().map(ExamTopicEntity::getId).collect(Collectors.toList()));
            Map<Long, List<ExamTopicOptionEntity>> optionMap = topicOptionEntityList.stream().collect(Collectors.groupingBy(ExamTopicOptionEntity::getTopicId));
            //获取知识点
            List<TopicKnowledgePvdRes> topicKnowledgeByTopicIds = topicKnowledgeIntegration.getTopicKnowledgeByTopicIds(examTopicEntities.stream().map(ExamTopicEntity::getId).collect(Collectors.toList()));
            List<ExamTopicInfoRes> examTopicInfoResList = examTopicEntities.stream().map(e->{
                List<ExamTopicOptionEntity> optionEntities = optionMap.get(e.getId());
                List<OptionRes> topicOptionListRes = new ArrayList<>();
                List<TopicKnowledgePvdRes> topicKnowledgePvdRes = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(topicKnowledgeByTopicIds)) {
                    topicKnowledgePvdRes = topicKnowledgeByTopicIds.stream().filter(topicKnowledge -> e.getId().equals(topicKnowledge.getTopicId())).collect(Collectors.toList());
                }
                if(!CollectionUtils.isEmpty(optionEntities)){
                    topicOptionListRes = optionEntities.stream()
                            .map(t -> OptionRes.builder()
                                    .option(t.getOption())
                                    .sort(t.getSort())
                                    .id(t.getId())
                                    .flag(t.getFlag())
                                    .build())
                            .collect(Collectors.toList());
                }
                ExamTopicInfoRes build = ExamTopicInfoRes.builder()
                        .id(e.getId())
                        .answer(e.getAnswer())
                        .analysis(e.getAnalysis())
                        .name(e.getName())
                        .type(e.getType())
                        .score(e.getScore())
                        .options(topicOptionListRes)
                        .knowledgeIds(topicKnowledgePvdRes)
                        .complexity(e.getComplexity())
                        .build();
                //是否选中
                if(!CollectionUtils.isEmpty(selectedTopicList)){
                    build.setSelected(selectedTopicList.contains(e.getId()));
                }

                return build;
            }).collect(Collectors.toList());
            return BasePageRes.newBuilder(examTopicInfoResList)
                    .addPageNo(pageNo)
                    .addPageSize(pageSize)
                    .addTotal(page.getTotal())
                    .build();
        }
        return BasePageRes.empty(NumberUtils.LONG_ZERO);
    }

    /**
     * 从考试中选择->勾选接口
     * @param targetTopicSelectReq 从考卷中导入请求参数
     * <AUTHOR>
     * @date 2021/06/10 12:00
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisDistributedLock(keys = {
            "T(java.lang.String).format(T(com.ioie.tac.web.constant.Constants).LOCK_EXAM_IMPORT_TARGET, #targetTopicSelectReq.targetId)"})
    public void topicExamSelected(TargetTopicSelectReq targetTopicSelectReq) {
        List<Long> topicIds = targetTopicSelectReq.getTopicIds();
        if(CollectionUtils.isEmpty(topicIds)){
            return;
        }

        //1.根据题目id获取题目列表
        List<ExamTopicEntity> examTopicEntityList = examTopicDao.selectBatchIds(topicIds);

        List<TopicExamTargetEntity> topicQuizTargetEntities = examTopicEntityList.stream()
                .map(e -> TopicExamTargetEntity.builder()
                        .examId(e.getExamId())
                        .targetId(targetTopicSelectReq.getTargetId())
                        .targetType(targetTopicSelectReq.getTargetType())
                        .topicId(e.getId())
                        .build())
                .collect(Collectors.toList());
        //2.插入数据
        if(CollectionUtils.isNotEmpty(topicQuizTargetEntities)) {
            topicExamTargetDao.batchInsert(topicQuizTargetEntities);
        }

        List<ExamTopicOptionEntity> examTopicOptionEntityList = examTopicOptionDao.selectByTopicIds(targetTopicSelectReq.getTopicIds());
        Map<Long, List<ExamTopicOptionEntity>> finalTopicOptions = examTopicOptionEntityList.stream().collect(Collectors.groupingBy(ExamTopicOptionEntity::getTopicId));

        //获取题目列表
        List<TopicLibraryPvdRes> topicLibraryPvdRes = examTopicEntityList.stream().map(e->{
                    TopicLibraryPvdRes res= TopicLibraryPvdRes.builder()
                            .name(e.getName())
                            .answer(e.getAnswer())
                            .analysis(e.getAnalysis())
                            .createType(e.getCreateType())
                            .type(e.getType())
                            .score(e.getScore())
                            .oriTopicId(e.getId())
                            // v220 增加难度评级
                            .complexity(NumberUtils.INTEGER_ZERO.equals(e.getComplexity()) ? NumberUtils.INTEGER_ONE : e.getComplexity())
                            .build();
                    res.setId(e.getId());
                    if(MapUtils.isNotEmpty(finalTopicOptions)){
                        List<ExamTopicOptionEntity> topicOptionEntityList = finalTopicOptions.get(e.getId());
                        if(!org.springframework.util.CollectionUtils.isEmpty(topicOptionEntityList)){
                            List<TopicOptionPvdRes> options= topicOptionEntityList.stream().map(x->
                                    TopicOptionPvdRes.builder()
                                            .flag(x.getFlag() == null ? Boolean.TRUE : x.getFlag())
                                            .option(x.getOption())
                                            .sort(x.getSort())
                                            .topicId(e.getId())
                                            .build()
                            ).collect(Collectors.toList());
                            res.setOptionPvdRes(options);
                        }
                    }
                    return res;
                }
        ).collect(Collectors.toList());

        //判断导入资源类型, 为考试时，需要将答案A,B,C,D转换为id形式
        if(TargetTypeEnum.TEST.getValue().equals(targetTopicSelectReq.getTargetType()) && CollectionUtils.isNotEmpty(topicLibraryPvdRes)){
            topicLibraryPvdRes = topicLibraryPvdRes.stream()
                    .peek(topicLibraryRes -> {
                        String type = topicLibraryRes.getType();
                        //判断类型为单选、多选，处理题目答案
                        if(TestTopicTypeEnum.SINGLECHOICE.getSqlValue().equals(type) || TestTopicTypeEnum.MULTIPLECHOICE.getSqlValue().equals(type)){
                            List<TopicOptionPvdRes> optionPvdRes = topicLibraryRes.getOptionPvdRes();
                            String newAnswer = "";
                            //选项列表不为空，将A,B,C,D答案转换为具体的id
                            if(CollectionUtils.isNotEmpty(optionPvdRes)){
                                for (int i = 0; i < optionPvdRes.size(); i++) {
                                    //为正确选项答案，转化为选项A,B,C,D
                                    if(optionPvdRes.get(i).getFlag()){
                                        newAnswer = newAnswer + optionPvdRes.get(i).getOptionId() + ",";
                                    }
                                }
                            }
                            //重新设置答题
                            if(StringUtils.isNotEmpty(newAnswer)){
                                newAnswer = newAnswer.substring(0, newAnswer.length() -1 );
                            }
                            topicLibraryRes.setAnswer(newAnswer);
                        }
                    })
                    .collect(Collectors.toList());
        }

        //添加选择的资源到目标表中
        addTargetSelectedSource(targetTopicSelectReq, topicImportTargetIntegration, topicLibraryPvdRes, examTopicDao, examTopicOptionDao, topicKnowledgeIntegration);

    }

    /**
     * 从考试中选择->取消勾选接口
     * @param targetTopicSelectReq 从考卷中导入请求参数
     * <AUTHOR>
     * @date 2021/06/10 12:00
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisDistributedLock(keys = {
            "T(java.lang.String).format(T(com.ioie.tac.web.constant.Constants).LOCK_DEL_EXAM_IMPORT_TARGET, #targetTopicSelectReq.targetId)"})
    public void topicExamUnSelected(TargetTopicSelectReq targetTopicSelectReq) {
        //1.删除中间表数据
        List<TopicExamTargetEntity> examTargetEntityList = topicExamTargetDao.getListTargetIdAndTopicIds(targetTopicSelectReq.getTargetId(), targetTopicSelectReq.getTopicIds());
        if(CollectionUtils.isNotEmpty(examTargetEntityList)){
            topicExamTargetDao.deleteBatchIds(examTargetEntityList.stream().map(TopicExamTargetEntity::getId).collect(Collectors.toList()));
        }
        topicImportTargetIntegration.deleteTopicQuizTarget(targetTopicSelectReq);
        //2.根据目标类型，删除不同表的数据  { TargetTypeEnum }
        deleteTargetSelectedSource(targetTopicSelectReq, topicImportTargetIntegration, examTopicDao, examTopicOptionDao, topicKnowledgeIntegration);

    }

    /**
     * 从系统中选择 -> 修改题目分数
     * @param targetTopicUpdateScoreReq 修改分数请求参数
     * <AUTHOR>
     * @date 2021/06/10 12:00
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisDistributedLock(keys = {
            "T(java.lang.String).format(T(com.ioie.tac.web.constant.Constants).LOCK_TARGET_UPDATE_TOPIC_SCORE, #targetTopicUpdateScoreReq.topicId)"})
    public void updateTopicScore(TargetTopicUpdateScoreReq targetTopicUpdateScoreReq) {

        switch (targetTopicUpdateScoreReq.getSourceType()) {
            case 0:
                //考试
                topicImportTargetIntegration.updateTestTopic(targetTopicUpdateScoreReq);
                break;
            case 1:
                //试卷
                topicImportTargetIntegration.updateQuizTopic(targetTopicUpdateScoreReq);
                break;
            case 2:
                ExamTopicEntity examTopicEntity = examTopicDao.selectById(targetTopicUpdateScoreReq.getTopicId());
                examTopicEntity.setScore(targetTopicUpdateScoreReq.getScore());
                examTopicDao.updateById(examTopicEntity);
                break;
            case 3:
                //题库
                topicImportTargetIntegration.updatePackageTopic(targetTopicUpdateScoreReq);
                break;
        }
    }

    /**
     * 老师-批阅试卷 - 退回操作
     * @param examAnswerId 答题卡id
     * @param quizGoBackReq 退回时长
     * <AUTHOR>
     * @date 2020/12/3 11:17
     * @return {@link OpRes}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void quizGoBack(Long examAnswerId, GoBackReq quizGoBackReq) {
        ExamStudentAnswerEntity examStudentAnswerEntity = examStudentAnswerDao.selectById(examAnswerId);
        //设置
        examStudentAnswerEntity.setGoBackFlag(1);
        examStudentAnswerEntity.setGoBackTime(quizGoBackReq.getGoBackTime());
        examStudentAnswerEntity.setGoBackEndTime(0L);
        examStudentAnswerEntity.setGoBackSignTime(0L);
        examStudentAnswerEntity.setGoBackSubmitTime(0L);
        // 分数清空
        examStudentAnswerEntity.setScore(NumberUtils.DOUBLE_ZERO);
        examStudentAnswerEntity.setSubmitStatus(0);
        examStudentAnswerEntity.setCheckStatus(0);
        //更新
        examStudentAnswerDao.updateById(examStudentAnswerEntity);
        //学生答案列表，批改状态清空
        List<ExamStudentOptionEntity> stuOptions = examStudentOptionDao.getListByAnswerId(examStudentAnswerEntity.getId());
        if(CollectionUtils.isNotEmpty(stuOptions)){
            stuOptions = stuOptions.stream()
                    .peek(stuOption -> {
                        stuOption.setCheckStatus(NumberUtils.INTEGER_ZERO);
                        // 清空答案分数
                        stuOption.setScore(NumberUtils.DOUBLE_MINUS_ONE);
                    })
                    .collect(Collectors.toList());
            examStudentOptionDao.updateBatch(stuOptions);
        }

        // 发送退回消息，进行扣分处理
        CompletableFuture.runAsync(() -> {
            Long mid = examStudentAnswerEntity.getMemberId();
            TenantMemberPvdRes memberPvdRes = tenantMemberIntegration.listByMemberIds(Lists.newArrayList(mid)).get(NumberUtils.INTEGER_ZERO);

            sendCalculateMsgUtil.sendCalculateMsgV1(CalculateGrowthMsg.builder()
                    .dataId(examStudentAnswerEntity.getExamId())
                    .memberId(mid)
                    .uid(memberPvdRes.getUid())
                    .scoreDataTypeEnum(CalculateStuScoreDataTypeEnum.EXAM)
                    .tenantId(memberPvdRes.getTenantId())
                    // 增加当前时间戳
                    .messageTime(DateHelper.instance().currentMills())
                    .goBackFlag(NumberUtils.INTEGER_ONE)
                    .build());
        });
    }

    /**
     * 老师-批阅试卷 - 补考
     * @param examId 考试id
     * @param memberId 成员id
     * @return 操作提示
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int quizResit(Long examId, Long memberId) {

        // 1.查询考生考试答题卡信息
        ExamStudentAnswerEntity studentAnswerEntity = examStudentAnswerDao.getAnswerByExamIdAndMemberId(memberId, examId);
        if (Optional.ofNullable(studentAnswerEntity).isPresent()) {
            // 已经设置学生补考
            if (NumberUtils.INTEGER_ONE.equals(studentAnswerEntity.getResitFlag())) {
                throw ExamActivityError.STUDENT_RESIT_EXIST_ERROR.error();
            }
            if (NumberUtils.INTEGER_ZERO.equals(studentAnswerEntity.getResitFlag())) {
                studentAnswerEntity.setResitFlag(NumberUtils.INTEGER_ONE);
                int result = examStudentAnswerDao.updateById(studentAnswerEntity);
                if (result > 0) {
                    // 消息推送学生补考信息
                    CompletableFuture.runAsync(() -> {
                        ArrayList<Long> memberIds = new ArrayList<>();
                        memberIds.add(memberId);
                        getuiAsync.pushResitExamMsg(examId, memberIds, AppExamOrTestMessageEnum.RESIT);
                    });
                }
                return result;
            }
        }

        // 2.新创建一份考试答题卡
        ExamStudentAnswerEntity entity = ExamStudentAnswerEntity.builder()
                .examId(examId)
                .resitFlag(NumberUtils.INTEGER_ONE)
                .memberId(memberId)
                .build();

        int result = examStudentAnswerDao.insert(entity);
        if (result > 0) {
            // 消息推送学生补考信息
            CompletableFuture.runAsync(() -> {
                ArrayList<Long> memberIds = new ArrayList<>();
                memberIds.add(memberId);
                getuiAsync.pushResitExamMsg(examId, memberIds, AppExamOrTestMessageEnum.RESIT);
            });
        }
        return result;
    }

    /**
     * 老师-批阅试卷 - 取消补考
     * @param examId 考试id
     * @param memberId 成员id
     * @return 操作提示
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int quizCancelResit(Long examId, Long memberId) {

        // 1.查询考生答题卡信息
        ExamStudentAnswerEntity studentAnswerEntity = examStudentAnswerDao.getAnswerByExamIdAndMemberId(memberId, examId);
        int result = 0;
        if (Optional.ofNullable(studentAnswerEntity).isPresent()) {
            // 2.学生已经在考试，抛出异常
            if (studentAnswerEntity.getSignTime() > 0) {
                throw ExamActivityError.STUDENT_RESIT_ERROR.error();
            }
            result = examStudentAnswerDao.deleteById(studentAnswerEntity.getId());
            if (result > 0) {
                // 消息推送学生补考信息
                CompletableFuture.runAsync(() -> {
                    ArrayList<Long> memberIds = new ArrayList<>();
                    memberIds.add(memberId);
                    getuiAsync.pushResitExamMsg(examId, memberIds, AppExamOrTestMessageEnum.CANCEL_RESIT);
                });
            }
            return result;
        }
        return result;
    }

    /**
     * 获取考试头部信息
     * <AUTHOR>
     * @since 2021/06/23 00:00:00
     * @param examId 考试id
     * @return {@link ExamHeaderRes}
     */
    @Override
    public ExamHeaderRes examHeaderInfo(Long examId) {
        ExamActivityEntity examActivityEntity = examActivityDao.selectById(examId);
        if(!Optional.ofNullable(examActivityEntity).isPresent()){
            throw BaseCmmError.RECORD_NOT_EXIST.error();
        }
        TenantMemberPvdRes tenantMemberPvdRes = tenantMemberIntegration.getByMemberId(Long.parseLong(examActivityEntity.getInsertUserId()));
        return ExamHeaderRes.builder()
                .introduction(examActivityEntity.getIntroduction())
                .examName(examActivityEntity.getName())
                .userName(tenantMemberPvdRes.getName())
                .insertTime(examActivityEntity.getInsertTime())
                .status(examActivityEntity.getPublishStatus())
                .build();
    }


    @Override
    public void replaceNameCode() {

        //进行查询题目乱码问题数据
        List<ExamTopicEntity> list =examActivityDao.selectContainsName("<!--[if gte mso 9]><xml>");
        if(list.size()>0){
            for (ExamTopicEntity t: list) {
                examActivityDao.updateNameAnswer(t.getId(),clearWordFormat(t.getName()),clearWordFormat(t.getAnswer()),clearWordFormat(t.getAnalysis()));
            }

        }
    }

    public static String clearWordFormat(String content) {

        content = content.replaceAll("<(?i)SPANYES[^>]*>", "");

        content = content.replaceAll("<(\\w[^>]*)  class=([^|>]*)([^>]*)", "<$1$3");

        content = content.replaceAll("<(\\w[^>]*)  style=\"([^\"]*)\"([^>]*)", "<$1$3");

        content = content.replaceAll("<(\\w[^>]*)  lang=([^|>]*)([^>]*)","<$1$3");

        content = content.replaceAll("<\\?\\?xml[^>]*>","");

        content = content.replaceAll("<\\/?\\w+:[^>]*>","");

        content = content.replaceAll("&nbsp;","");

        content = content.replaceAll("\\n(\\n)*( )*(\\n)*\\n","\n");

        content = content.replaceAll("<!--\\[if [^*]*?>[\\s\\s]*?<!\\[endif[^*]*?>","");

        content = content.replaceAll("<!\\[if [^*]*?>[\\s\\s]*?<!\\[endif[^*]*?>","");

        content = content.replaceAll("<style[^>]*?>[\\s\\S]*?<\\/style>","");

        content = content.replaceAll("<!--\\[if [^*]*?>[\\s\\s]*?<!\\[endif[^*]*?>","");


        return content;
    }


    /**
     * 根据学生考试答题卡id获取考试结果
     * @param examPaperId 考试试卷id
     * @param stuExamIds 学生考试答题卡id
     * @return {@link List<StuExamPDFRes>}
     */
    private List<StuExamPDFRes> getStuExamResult(Long examPaperId, List<Long> stuExamIds) {

        // 1.考试实体详情
        ExamActivityEntity examEntity = examActivityDao.selectById(examPaperId);
        if (!Optional.ofNullable(examEntity).isPresent()) {
            return new ArrayList<>();
        }

        // 2.根据答题卡id获取答题卡详情
        List<ExamStudentAnswerEntity> studentExamList = examStudentAnswerDao.selectBatchIds(stuExamIds);
        if (CollectionUtils.isEmpty(studentExamList)) {
            return new ArrayList<>();
        }
        // key -> 学生考试答题卡id value -> 答题卡实体详情
        Map<Long, ExamStudentAnswerEntity> stuExamMap = studentExamList.parallelStream()
                .collect(Collectors.toMap(ExamStudentAnswerEntity::getId, a -> a));

        // 3.成员基本信息
        List<Long> memberIds = studentExamList.parallelStream().map(ExamStudentAnswerEntity::getMemberId).collect(Collectors.toList());
        List<TenantMemberPvdRes> tenantMemberPvdRes = tenantMemberIntegration.listByMemberIds(memberIds);
        // key -> 成员id value -> left：姓名 right：学号
        Map<Long, TenantMemberPvdRes> memberInfoMap = tenantMemberPvdRes.parallelStream()
                .collect(Collectors.toMap(TenantMemberPvdRes::getId, a -> a, (a, b) -> a));

        // 4.学生考试答题卡选项数据
        List<ExamStudentOptionEntity> stuExamOptionLists = examStudentOptionDao.getListByStuAnswerIds(stuExamIds);
        // key -> 学生考试答题卡id value -> 学生考试答题卡选项数据列表
        Map<Long, List<ExamStudentOptionEntity>> stuExamOptionMap = stuExamOptionLists.parallelStream()
                .collect(Collectors.groupingBy(ExamStudentOptionEntity::getAnswerId));

        // 5.根据考试id获取试题列表及实体总分
        List<ExamTopicEntity> examTopicEntityList = examTopicDao.getListByExamId(examPaperId);
        if (CollectionUtils.isEmpty(examTopicEntityList)) {
            return new ArrayList<>();
        }

        // 6.查询所有测试答题卡评语数据
        List<StuRecordRemarkEntity> remarkEntityList = stuRecordRemarkDao.selectByRecordIds(stuExamIds);
        // key -> 学生测试答题卡id，value -> 学生测试答题卡评语
        Map<Long, String> stuExamRemarkMap = remarkEntityList.parallelStream()
                .collect(Collectors.toMap(
                        StuRecordRemarkEntity::getRecordId,
                        StuRecordRemarkEntity::getRemark,
                        (a, b) -> a));

        List<StuExamPDFRes> resList = new ArrayList();
        if (MapUtils.isNotEmpty(stuExamMap)) {
            // 考试试卷总分
            AtomicReference<Double> score = new AtomicReference<>(NumberUtils.DOUBLE_ZERO);
            // 客观题总分
            AtomicReference<Double> objectScore = new AtomicReference<>(NumberUtils.DOUBLE_ZERO);
            // 主观题总分
            AtomicReference<Double> subjectScore = new AtomicReference<>(NumberUtils.DOUBLE_ZERO);

            stuExamMap.entrySet().parallelStream().forEach(stuExam -> {

                // 学生考试答题卡id
                Long stuExamId = stuExam.getKey();
                // 学生考试答题卡实体
                ExamStudentAnswerEntity stuExamEntity = stuExam.getValue();
                // 成员id
                Long memberId = stuExamEntity.getMemberId();
                // 成员对应的姓名和学号
                TenantMemberPvdRes memberPvdRes = memberInfoMap.get(memberId);
                if (Optional.ofNullable(memberPvdRes).isPresent()) {
                    //学生客观题总分
                    AtomicDouble stuObjectScore = new AtomicDouble(NumberUtils.DOUBLE_ZERO);
                    //学生主观题总分
                    AtomicDouble stuSubjectScore = new AtomicDouble(NumberUtils.DOUBLE_ZERO);



                    StuExamPDFRes stuExamPDFRes = StuExamPDFRes.builder()
                            .examName(examEntity.getName())
                            .stuExamId(stuExamEntity.getExamId())
                            .submitTime(stuExamEntity.getSubmitTime())
                            .build();

                    // 姓名
                    String name = memberPvdRes.getName();
                    stuExamPDFRes.setName(name);
                    // 学号
                    String no = memberPvdRes.getNo();
                    stuExamPDFRes.setNo(no);
                    // 手机后缀
                    String phone = memberPvdRes.getPhone();
                    stuExamPDFRes.setPhoneSuffix(phone.substring(7));

                    // 学生成绩
                    Double stuScore = stuExamEntity.getScore();

                    // 获取学生答题卡对应的答题选项数据
                    List<ExamStudentOptionEntity> stuExamOptionList = stuExamOptionMap.get(stuExamId);
                    if (NumberUtils.DOUBLE_ZERO.equals(stuScore) && CollectionUtils.isNotEmpty(stuExamOptionList)) {
                        stuScore = stuExamOptionList.stream()
                                .filter(e -> !NumberUtils.DOUBLE_MINUS_ONE.equals(e.getScore()))
                                .mapToDouble(ExamStudentOptionEntity::getScore)
                                .sum();
                    }
                    stuExamPDFRes.setStuScore(stuScore);

                    // 统计总分
                    score.set(examTopicEntityList.stream().mapToDouble(ExamTopicEntity::getScore).sum());
                    stuExamPDFRes.setScore(score.get());

                    //统计客观题总分
                    objectScore.set(examTopicEntityList.stream()
                            .filter(e -> TestTopicTypeEnum.SINGLECHOICE.getSqlValue().equals(e.getType()) || TestTopicTypeEnum.MULTIPLECHOICE.getSqlValue().equals(e.getType()) || TestTopicTypeEnum.JUDGETOPIC.getSqlValue().equals(e.getType()))
                            .mapToDouble(ExamTopicEntity::getScore)
                            .sum());
                    stuExamPDFRes.setObjectScore(objectScore.get());

                    // 统计主观题总分
                    subjectScore.set(examTopicEntityList.stream()
                            .filter(e -> TestTopicTypeEnum.COMPLETION.getSqlValue().equals(e.getType()) || TestTopicTypeEnum.SHORTANSWER.getSqlValue().equals(e.getType()))
                            .mapToDouble(ExamTopicEntity::getScore)
                            .sum());
                    stuExamPDFRes.setSubjectScore(subjectScore.get());

                    List<Long> topicIds = examTopicEntityList.stream()
                            .filter(e -> TestTopicTypeEnum.SINGLECHOICE.getSqlValue().equals(e.getType()) || TestTopicTypeEnum.MULTIPLECHOICE.getSqlValue().equals(e.getType()))
                            .map(ExamTopicEntity::getId)
                            .collect(Collectors.toList());

                    //按题目id分组
                    Map<Long, List<ExamTopicOptionEntity>> topicOptionMap = new HashMap<>(16);
                    if (CollectionUtils.isNotEmpty(topicIds)) {
                        //存在单选/多选
                        List<ExamTopicOptionEntity> examTopicOptionEntityList = examTopicOptionDao.getListByTopicIds(topicIds);
                        topicOptionMap = examTopicOptionEntityList.stream().collect(Collectors.groupingBy(ExamTopicOptionEntity::getTopicId));
                    }
                    Map<Long, List<ExamTopicOptionEntity>> finalTopicOptionMap = topicOptionMap;
                    AtomicInteger atomicInteger = new AtomicInteger(1);

                    List<ExamTopicAnswerPDFRes> topicAnswerPDFResList = examTopicEntityList.stream()
                            .map(e -> {
                                ExamTopicAnswerPDFRes examTopicListRes = BeanHelper.copyObject(e, ExamTopicAnswerPDFRes.class);
                                examTopicListRes.setStuMark(ExamStuMarkEnum.NO_CORRECT.getValue());
                                examTopicListRes.setScore(e.getScore().doubleValue());
                                String answer = "";
                                if (CollectionUtils.isNotEmpty(stuExamOptionList)) {
                                    ExamStudentOptionEntity examStudentOptionEntity = stuExamOptionList.stream().filter(t -> t.getTopicId().equals(e.getId())).findAny().orElse(null);
                                    if (examStudentOptionEntity != null) {
                                        answer = examStudentOptionEntity.getAnswer();
                                        examTopicListRes.setStuAnswer(answer);
                                        //学生-题目得分
                                        Double stuTopicScore = examStudentOptionEntity.getScore();
                                        if (!NumberUtils.DOUBLE_MINUS_ONE.equals(stuTopicScore)) {
                                            //客观题
                                            if (TestTopicTypeEnum.SINGLECHOICE.getSqlValue().equals(e.getType())
                                                    || TestTopicTypeEnum.MULTIPLECHOICE.getSqlValue().equals(e.getType())
                                                    || TestTopicTypeEnum.JUDGETOPIC.getSqlValue().equals(e.getType())) {
                                                stuObjectScore.getAndAdd(stuTopicScore);
                                            } else {
                                                stuSubjectScore.getAndAdd(stuTopicScore);
                                            }
                                        }
                                        examTopicListRes.setStuScore(stuTopicScore);
                                        Integer checkStatus = examStudentOptionEntity.getCheckStatus();
                                        //题目分数
                                        Double topicScore = e.getScore().doubleValue();
                                        //默认设置未批
                                        examTopicListRes.setStuMark(ExamStuMarkEnum.NO_CORRECT.getValue());
                                        //设置题卡 得分情况
                                        if (checkStatus == 1) {
                                            if (stuTopicScore.equals(topicScore)) {
                                                examTopicListRes.setStuMark(ExamStuMarkEnum.FULL_SCORE.getValue());
                                            } else if (stuTopicScore > 0) {
                                                examTopicListRes.setStuMark(ExamStuMarkEnum.PART_SCORE.getValue());
                                            } else if (NumberUtils.DOUBLE_ZERO.equals(stuTopicScore)) {
                                                examTopicListRes.setStuMark(ExamStuMarkEnum.ZERO_SCORE.getValue());
                                            }
                                        }
                                    }
                                }
                                if (MapUtils.isNotEmpty(finalTopicOptionMap)) {
                                    //匹配试题id
                                    List<ExamTopicOptionEntity> examTopicOptionEntities = finalTopicOptionMap.get(e.getId());
                                    if (CollectionUtils.isNotEmpty(examTopicOptionEntities)) {
                                        //设置选项列表
                                        examTopicListRes.setOptions(getPDFTopicOptionList(examTopicOptionEntities, answer));
                                    }
                                }
                                //重新设置排序，从1开始
                                examTopicListRes.setSort(atomicInteger.getAndAdd(1));
                                return examTopicListRes;
                            })
                            .collect(Collectors.toList());

                    List<ExamTopicCardRes> topicCardList = topicAnswerPDFResList.parallelStream().map(o ->
                            ExamTopicCardRes.builder()
                                    .topicId(o.getId())
                                    .stuMark(o.getStuMark())
                                    .build()).collect(Collectors.toList());
                    stuExamPDFRes.setTopics(topicAnswerPDFResList);
                    stuExamPDFRes.setTopicCardList(topicCardList);
                    stuExamPDFRes.setStuObjectScore(stuObjectScore.get());
                    stuExamPDFRes.setStuSubjectScore(stuSubjectScore.get());

                    // 设置评语
                    String remark = stuExamRemarkMap.getOrDefault(stuExamId, Strings.EMPTY);
                    stuExamPDFRes.setRemark(remark);
                    resList.add(stuExamPDFRes);
                }
            });
        }
        return resList;
    }

    /**
     * 获取试题选项列表
     * @param examTopicOptionEntities 试题选项列表
     * @param finalAnswer 学生答案
     * @return 试题选项列表
     */
    public static List<TopicOptionPDFRes> getPDFTopicOptionList(List<ExamTopicOptionEntity> examTopicOptionEntities,String finalAnswer){
        return examTopicOptionEntities.stream()
                .map(t->{
                    TopicOptionPDFRes topicOptionRes = new TopicOptionPDFRes();
                    topicOptionRes.setOptionId(t.getId());
                    topicOptionRes.setOption(t.getOption());
                    topicOptionRes.setFlag(t.getFlag());
                    topicOptionRes.setSort(t.getSort());
                    List<Long> answers = new ArrayList<>(NumberUtils.INTEGER_ONE);
                    if(StringUtils.isNotEmpty(finalAnswer)){
                        if(finalAnswer.contains(CmmConstants.ENG_COMMA)){
                            answers = Arrays.stream(
                                    finalAnswer.split(CmmConstants.ENG_COMMA))
                                    .filter(StringUtils::isNotEmpty)
                                    .map(Long::parseLong)
                                    .collect(Collectors.toList());
                        }else {
                            answers.add(Long.parseLong(finalAnswer));
                        }
                        // 判断选项是否被选中
                        if(CollectionUtils.isNotEmpty(answers) && answers.contains(topicOptionRes.getOptionId())){
                            topicOptionRes.setSelected(true);
                        }
                    }
                    topicOptionRes.setOptionId(t.getId());
                    return topicOptionRes;
                })
                .collect(Collectors.toList());
    }

    /**
     * app -> 点击考试消息展示基本信息
     * @param examId 考试id
     * @return {@link AppExamBaseInfoRes}
     */
    @Override
    public AppExamBaseInfoRes getExamBaseInfo(Long examId) {

        ExamActivityEntity entity = examActivityDao.selectById(examId);
        if (!Optional.ofNullable(entity).isPresent()) {
            throw new BizException("考试不存在");
        }

        return AppExamBaseInfoRes.builder()
                .examId(entity.getId())
                .examName(entity.getName())
                .startTime(entity.getStartMills())
                .totalTime(entity.getTimeLimit())
                .status(entity.getPublishStatus())
                .build();
    }

    /**
     * app -> 点击考试消息展示基本信息
     * @param req 请求参数
     * @return {@link AppExamBaseInfoRes}
     */
    @Override
    public ExamBaseInfoRes getTeachingExamBaseInfo(ExamBaseInfoReq req) {

        Long tenantId = req.getTenantId();
        Long examId = req.getExamId();
        Long memberId = req.getMemberId();

        // 判断租户id所在租户状态
        if (null != tenantId) {
            PermissionTenantInfoRes tenantInfoRes = tenantIntegration.getTenantInfoByTenantId(tenantId);

            // 租户没被删除
            if (Optional.ofNullable(tenantInfoRes).isPresent()) {
                // 租户有效性标识
                Integer tenantValidFlag = tenantInfoRes.getTenantValidFlag();
                // 租户已停用
                if (DataValidEnum.INVALID.getValue() == tenantValidFlag) {
                    // 链接失效，请联系管理员
                    throw ClassError.LINK_NOT_ALLOW.error();
                }
                // 租户正常判断租户成员状态
                else {
                    // 根据uid和租户id查询租户成员状态
                    List<TenantRes> tenantMemberList = memberIntegration.getByUid(UserInfoHolder.get().getUid());
                    if (CollectionUtils.isNotEmpty(tenantMemberList)) {
                        tenantMemberList.parallelStream()
                                .filter(e -> tenantId.equals(e.getTenantId()))
                                .findAny()
                                .ifPresent(tenantRes -> {
                                    // 审核状态
                                    Integer approvalStatus = tenantRes.getApprovalStatus();
                                    // 是否为停用状态
                                    Integer validFlag = tenantRes.getValidFlag();
                                    // 是否为体验学员
                                    Integer expFlag = tenantRes.getExpFlag();
                                    // 审核不通过或者已被停用
                                    if (!(ApprovalEnum.APPROVAL.getValue() == approvalStatus || (DataValidEnum.VALID.getValue() == expFlag))
                                            || DataValidEnum.INVALID.getValue() == validFlag) {
                                        throw ClassError.PERMISSION_NOT_ALLOW.error();
                                    }
                                });
                    }
                }
            }
            // 租户被删除了
            else {
                // 链接失效，请联系管理员
                throw ClassError.LINK_NOT_ALLOW.error();
            }
        }

        ExamActivityEntity entity = examActivityDao.selectById(examId);
        if (!Optional.ofNullable(entity).isPresent()) {
            throw new BizException("未找到该考试，请联系相关老师");
        }

        ExamBaseInfoRes.ExamBaseInfoResBuilder builder = ExamBaseInfoRes.builder();
        builder.examId(examId);
        builder.examName(entity.getName());
        builder.startTime(entity.getStartMills());
        builder.status(entity.getPublishStatus());

        // 判断是否为考试成员决定要不要跳转
        List<ExamActivityStudentEntity> memberList = examActivityStudentDao.selByExamId(examId);
        memberList.parallelStream()
                .filter(e -> e.getMemberId().equals(memberId))
                .findAny()
                .ifPresent(e -> {
            // 是否可以跳转
            builder.status(YesNoEnum.YES.getValue());
        });

        return builder.build();
    }

    /**
     * 首页-教师  我的考试-考官
     *
     * @return
     */
    @Override
    public List<TeaRecentExamRes> teaRecentExam() {
        Long memberId = TenantMemberHolder.get().getMemberId();
        //1、查询作为考官的考试ids数据
        List<Long> ids = examIdsByManage(memberId);
        if(CollectionUtils.isEmpty(ids)){
            return new ArrayList<>();
        }
        //1.1、根据状态排序 进行中、未开始、未发布（编辑中）、已结束 再根据时间排序
        List<ExamActivityEntity> examActivityList = examActivityDao.selRecentExam(ids);
        if(CollectionUtils.isEmpty(examActivityList)){
            return new ArrayList<>();
        }
        //2、截取前20条记录
        if(examActivityList.size()>20){
            examActivityList = examActivityList.subList(0,20);
        }
        //3、组装返回数据
        return examActivityList.stream().map(e->{
            //初始化数据
            Long startTime = NumberUtils.LONG_ZERO;
            Long timeLimit = NumberUtils.LONG_ZERO;
            if(ExamStatusEnum.NO_STARTED.getValue().equals(e.getPublishStatus())){
                //未开始
                startTime = e.getStartMills();
            }else if(ExamStatusEnum.PROCESS.getValue().equals(e.getPublishStatus())){
                //进行中
                startTime = e.getStartMills();
                LocalDateTime currentTime = DateHelper.instance().millisTimestamp2LocalDateTime(DateHelper.instance().currentMills());
                LocalDateTime endDateTime = DateHelper.instance().ofEpochMilli2DateTime(e.getEndMills());
                timeLimit =ChronoUnit.MINUTES.between(currentTime, endDateTime);
            }else if(ExamStatusEnum.FINISH.getValue().equals(e.getPublishStatus())){
                //已结束
                startTime = e.getStartMills();
            }
            return TeaRecentExamRes.builder()
                    .examId(e.getId())
                    .name(e.getName())
                    .examStatus(ExamStatusEnum.getCodeByValue(e.getPublishStatus()))
                    .startMills(startTime)
                    .timeLimit(timeLimit)
                    .build();
        }).collect(Collectors.toList());
    }

    /**
     * 批量删除从考试导入到目标表的中间表数据topic
     * @param targetTopicSelectReq 请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delTopicTarget(TargetTopicSelectReq targetTopicSelectReq) {
        List<TopicExamTargetEntity> listTargetIdAndTopicIds = topicExamTargetDao.getListTargetIdAndTopicIds(targetTopicSelectReq.getTargetId(), targetTopicSelectReq.getTopicIds());
        // 删除 从考试中导入目标的数据
        if(CollectionUtils.isNotEmpty(listTargetIdAndTopicIds)){
            List<Long> targetIds = listTargetIdAndTopicIds.parallelStream()
                    .map(TopicExamTargetEntity::getId)
                    .collect(Collectors.toList());
            topicExamTargetDao.deleteBatchIds(targetIds);
        }
    }

    @Override
    public List<ExamTopicInfoRes> getTopics(Long examId, String topicType, String complexity, String keyWordType, String keyWord, Long knowledgeId) {
        // 试卷基础信息
        ExamActivityEntity examActivity = examActivityDao.selectById(examId);
        if (!Optional.ofNullable(examActivity).isPresent()) {
            throw CommonError.RECORD_NOT_EXIST.error("试卷");
        }

        // 通过试卷获取题目
        List<ExamTopicEntity> topics = examTopicDao.selByPaperIds(Stream.of(examId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(topics)) {
            return new ArrayList<>();
        }

        // 题型
        if (StringUtils.isNotBlank(topicType)) {

            List<String> allTopicTypes = Arrays.stream(TestTopicTypeEnum.values()).map(TestTopicTypeEnum::getSqlValue).collect(Collectors.toList());
            // 多个用逗号分割
            List<String> topicTypes = Arrays.stream(topicType.split(CmmConstants.ENG_COMMA)).filter(new Predicate<String>() {
                @Override
                public boolean test(String s) {
                    return allTopicTypes.contains(s);
                }
            }).collect(Collectors.toList());
            topics = topics.parallelStream().filter(new Predicate<ExamTopicEntity>() {
                @Override
                public boolean test(ExamTopicEntity entity) {
                    return topicTypes.contains(entity.getType());
                }
            }).collect(Collectors.toList());
        }

        // 难易程度
        if (StringUtils.isNotBlank(complexity)) {
            List<Integer> complexityList = Arrays.stream(complexity.split(CmmConstants.ENG_COMMA)).filter(new Predicate<String>() {
                @Override
                public boolean test(String s) {
                    return StringUtils.isNumeric(s);
                }
            }).map(new Function<String, Integer>() {
                @Override
                public Integer apply(String s) {
                    return Integer.parseInt(s);
                }
            }).collect(Collectors.toList());
            topics = topics.parallelStream().filter(new Predicate<ExamTopicEntity>() {
                @Override
                public boolean test(ExamTopicEntity entity) {
                    return complexityList.contains(entity.getComplexity());
                }
            }).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(topics)) {
            return new ArrayList<>();
        }

        List<Long> topicIds = topics.stream().map(ExamTopicEntity::getId).collect(Collectors.toList());
        // 获取选项列表
        List<ExamTopicOptionEntity> topicOptions = examTopicOptionDao.selectByTopicIds(topicIds);

        // 关键字
        if (StringUtils.isNotBlank(keyWord)) {
            if ("topic".equals(keyWordType)) {
                // 题干关键字查询
                topics = topics.parallelStream().filter(new Predicate<ExamTopicEntity>() {
                    @Override
                    public boolean test(ExamTopicEntity entity) {
                        return entity.getName().contains(keyWord);
                    }
                }).collect(Collectors.toList());
            } else {
                // 选项关键字查询
                // 符合条件的选项的试题id集合
                List<Long> optionTopicIds = topicOptions.parallelStream().filter(new Predicate<ExamTopicOptionEntity>() {
                    @Override
                    public boolean test(ExamTopicOptionEntity entity) {
                        return entity.getOption().contains(keyWord);
                    }
                }).map(ExamTopicOptionEntity::getTopicId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(optionTopicIds)) {
                    return new ArrayList<>();
                }
                // 过滤符合条件的试题
                topics = topics.parallelStream().filter(new Predicate<ExamTopicEntity>() {
                    @Override
                    public boolean test(ExamTopicEntity entity) {
                        return optionTopicIds.contains(entity.getId());

                    }
                }).collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(topics)) {
            return new ArrayList<>();
        }

        // key - 试题id; value -试题选项列表
        Map<Long, List<OptionRes>> topicOptionMap = topicOptions.parallelStream()
                .collect(Collectors.groupingBy(ExamTopicOptionEntity::getTopicId,
                        Collectors.mapping(x -> OptionRes.builder()
                                .id(x.getId())
                                .sort(x.getSort())
                                .flag(x.getFlag())
                                .option(x.getOption())
                                .build(), Collectors.toList())));

        // 根据题目ids，获取知识点相关数据
        List<TopicKnowledgePvdRes> topicKnowledgeList = topicKnowledgeIntegration.getTopicKnowledgeByTopicIds(topicIds);
        // key - 试题id; value - 知识点数据
        Map<Long, List<TopicKnowledgePvdRes>> topicKnowLedgeMap = topicKnowledgeList.parallelStream()
                .collect(Collectors.groupingBy(TopicKnowledgePvdRes::getTopicId));

        // 根据试题绑定的知识点查询试题列表
        if (Optional.ofNullable(knowledgeId).isPresent()) {
            // 为0表示获取未关联的试题选项列表
            if (knowledgeId == 0) {
                // 已绑定的知识点的试题id集合
                List<Long> bindKnowledgeTopicIds = new ArrayList<>(topicKnowLedgeMap.keySet());
                topics = topics.parallelStream().filter(new Predicate<ExamTopicEntity>() {
                    @Override
                    public boolean test(ExamTopicEntity entity) {
                        return !bindKnowledgeTopicIds.contains(entity.getId());

                    }
                }).collect(Collectors.toList());
            } else {
                // key - 知识点id; value - 知识点绑定的试题id集合
                Map<Long, List<Long>> knowledgeTopicIdsMap = topicKnowledgeList.parallelStream()
                        .collect(Collectors.groupingBy(TopicKnowledgePvdRes::getId, Collectors.mapping(TopicKnowledgePvdRes::getTopicId, Collectors.toList())));
                List<Long> bindKnowledgeTopicIds = knowledgeTopicIdsMap.get(knowledgeId);
                if (CollectionUtils.isNotEmpty(bindKnowledgeTopicIds)) {
                    // 获取具体绑定该知识点的试题选项列表
                    topics = topics.parallelStream().filter(new Predicate<ExamTopicEntity>() {
                        @Override
                        public boolean test(ExamTopicEntity entity) {
                            return bindKnowledgeTopicIds.contains(entity.getId());

                        }
                    }).collect(Collectors.toList());
                }
            }
        }

        return topics.parallelStream().map(new Function<ExamTopicEntity, ExamTopicInfoRes>() {
            @Override
            public ExamTopicInfoRes apply(ExamTopicEntity e) {
                Long topicId = e.getId();
                return ExamTopicInfoRes.builder()
                        .id(topicId)
                        .name(e.getName())
                        .type(e.getType())
                        .sort(e.getSort())
                        .score(e.getScore())
                        .answer(e.getAnswer())
                        .analysis(e.getAnalysis())
                        .options(topicOptionMap.get(topicId))
                        .knowledgeIds(topicKnowLedgeMap.get(topicId))
                        .complexity(e.getComplexity())
                        .build();
            }
        }).collect(Collectors.toList());
    }

    @Override
    public List<TopicKnowLedgeSummaryPvdRes> getExamTopicSummary(Long examId) {

        // 试卷基础信息
        ExamActivityEntity examActivity = examActivityDao.selectById(examId);
        if (!Optional.ofNullable(examActivity).isPresent()) {
            throw CommonError.RECORD_NOT_EXIST.error("试卷");
        }
        // 通过试卷获取题目
        List<ExamTopicEntity> topics = examTopicDao.selByPaperIds(Stream.of(examId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(topics)) {
            return new ArrayList<>();
        }

        List<Long> topicIds = topics.stream().map(ExamTopicEntity::getId).collect(Collectors.toList());
        return this.topicKnowledgeIntegration.getSummaryKnowledgeByTopicIds(topicIds);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void importTopicsV1(Long examId, MultipartFile file) {
        //判定权限
        examMemberRoleService.checkPermission(examId, ExamMemberRoleEnum.UPDATE.getValue(), ExamModuleEnum.TOPIC.getValue());

        //文件 easyPoi 转换对象
        ExcelImportResult<ExamTopicExcelV1Req> importResult = ExcelUtils.importExcelCheck(file, NumberUtils.INTEGER_ZERO, NumberUtils.INTEGER_ONE, ExamTopicExcelV1Req.class);
        Workbook workbook = importResult.getWorkbook();
        workbook.getSheetAt(0);
        int sort = searchSort(examId, examTopicDao);
        // 注解校验数据有误进行输出返回
        if (importResult.isVerifyFail()) {
            //抛出异常
            String throwMsg = "";
            List<ExamTopicExcelV1Req> failList = importResult.getFailList();
            for (ExamTopicExcelV1Req excelReq : failList) {
                int rowNum = excelReq.getRowNum() + NumberUtils.INTEGER_ONE;
                String errorMsg = excelReq.getErrorMsg();
                throwMsg += "第" + rowNum + "行，" + errorMsg + "!";
            }
            throw ExamActivityError.EXCEL_NO_DATA.error(throwMsg);
        }
        // 需要绑定知识点的记录
        List<TopicBindKnowLedgePvdReq> bindKnowLedges = new ArrayList<>();

        List<ExamTopicOptionEntity> examTopicOptionEntities = new ArrayList<>();
        AtomicReference<String> typeErrorString = new AtomicReference<>(Strings.EMPTY);
        // 报错行数位置
        AtomicInteger index = new AtomicInteger(NumberUtils.INTEGER_TWO);
        AtomicInteger sortTopic = new AtomicInteger(sort);
        List<ExamTopicEntity> examTopicEntities = importResult.getList().stream().map(e -> {
            String answer = e.getAnswer();
            if (TestTopicTypeEnum.SINGLECHOICE.getValue().equals(e.getType())
                    || TestTopicTypeEnum.MULTIPLECHOICE.getValue().equals(e.getType())) {
                answer = "";
            }
            // 难易程度，默认一级
            Integer complexity = e.getComplexity();
            complexity = Optional.ofNullable(complexity).orElse(TopicComplexityEnum.BEHAVIOR_COMPLEXITY_ONE.getValue());

            ExamTopicEntity res = ExamTopicEntity.builder()
                    .examId(examId)
                    .oriTopicId(NumberUtils.LONG_ZERO)
                    .type(TestTopicTypeEnum.getTopicTypeSqlValue(e.getType()))
                    .createType(TopicCreateTypeEnum.IMPORT_TEST.getName())
                    .sort(sortTopic.getAndAdd(NumberUtils.INTEGER_ONE))
                    .complexity(complexity)
                    .score(e.getScore().intValue())
                    .level(0)
                    //导入富文本字段需转义处理
                    .name(changeStr(e.getName()))
                    .answer(changeStr(answer))
                    .analysis(changeStr(e.getAnalysis()))
                    .build();
            Long id = ContextHelper.getBean(SnowflakeIdWorker.class).nextId();
            res.setId(id);

            if (TestTopicTypeEnum.SINGLECHOICE.getValue().equals(e.getType())
                    || TestTopicTypeEnum.MULTIPLECHOICE.getValue().equals(e.getType())) {

                String[] options = TopicOptionEnum.OPTION.getArr();
                String optionA = options[0];
                String optionB = options[1];
                //选项格式不对已判定
                Boolean optionError = false;
                int i = NumberUtils.INTEGER_ZERO;
                for (String str : options) {
                    //选项为空 进入下一层循环
                    boolean flag = e.getAnswer().contains(str);
                    String option = ReflectionUtils.getFieldValue(e, TopicOptionEnum.OPTION.getName() + str);
                    if (StringUtils.isNotEmpty(option)) {
                        examTopicOptionEntities.add(ExamTopicOptionEntity.builder()
                                .topicId(id)
                                .option(option)
                                .flag(flag)
                                .sort(i)
                                .build());
                        i++;
                    } else {
                        //判定选项A、B是否有内容
                        if((optionB.equals(str) || optionA.equals(str)) && !optionError ){
                            optionError = true;
                            String errorMsg = typeErrorString.get();
                            errorMsg += "第" + index.get() + "行,选项未填写或格式不匹配，请将格式调整为文本格式后重新上传!</br>";
                            typeErrorString.set(errorMsg);
                        }
                        continue;
                    }
                }
            }
            // 校验导入的关联知识点
            String relateKnowledge = e.getRelateKnowledge();
            if (StringUtils.isNotBlank(relateKnowledge)) {
                TopicBindKnowLedgePvdReq bindKnowLedgePvdReq = new TopicBindKnowLedgePvdReq();
                bindKnowLedgePvdReq.setBindKnowLedge(relateKnowledge);
                bindKnowLedgePvdReq.setRowNum(index.get());
                bindKnowLedgePvdReq.setTopicId(id);
                bindKnowLedges.add(bindKnowLedgePvdReq);
            }
            index.getAndAdd(NumberUtils.INTEGER_ONE);
            return res;
        }).collect(Collectors.toList());

        // 关联知识点
        if (CollectionUtils.isNotEmpty(bindKnowLedges)) {
            TopicBindKnowLedgePvdRes bindKnowLedgePvdRes = this.topicKnowledgeIntegration.importExamTopicBindKnowLedge(bindKnowLedges);
            if (!bindKnowLedgePvdRes.isSuccess()) {
                String errMsg = bindKnowLedgePvdRes.getErrMsg();
                throw ExamActivityError.EXCEL_NO_DATA.error(errMsg);
            }
        }

        //异常抛出
        if(!org.springframework.util.StringUtils.isEmpty(typeErrorString.get())){
            String error = typeErrorString.get();
            throw ExamActivityError.EXCEL_NO_DATA.error(error);
        }

        // 批量插入
        if (!org.springframework.util.CollectionUtils.isEmpty(examTopicEntities)) {
            examTopicDao.batchInsert(examTopicEntities);
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(examTopicOptionEntities)) {
            examTopicOptionDao.batchInsert(examTopicOptionEntities);
        }
    }


    @Override
    public ExamCheckListV1Res checkQuizTaskListV1(TenantMemberInfo tenantMemberInfo, String id, String name, Integer status, String sortRule, int pageNo, int pageSize) {

        if (StringUtils.isBlank(id) || !NumberUtils.isDigits(id)) {
            throw CommonError.RECORD_NOT_EXIST.error("考试");
        }
        Long examId = Long.parseLong(id);
        // 获取考试详情
        ExamActivityEntity examActivity = examActivityDao.selectById(examId);
        if (!Optional.ofNullable(examActivity).isPresent()) {
            throw CommonError.RECORD_NOT_EXIST.error("考试");
        }
        Integer publishStatus = examActivity.getPublishStatus();
        // 考试考试时间
        Long startMills = examActivity.getStartMills();
        // 考试时长，单位分钟
        Integer timeLimit = examActivity.getTimeLimit();
        ExamCheckListV1Res.ExamCheckListV1ResBuilder builder = ExamCheckListV1Res.builder()
                .name(examActivity.getName())
                .insertTime(examActivity.getInsertTime())
                .examId(examId)
                .endTime(startMills + timeLimit)
                .publishStatus(publishStatus);


        // 查看考试成员id集合
        List<Long> examMemberIds = this.examActivityStudentDao.getMemberIdsByExamId(examId);
        if (CollectionUtils.isEmpty(examMemberIds)) {
            return builder.page(BasePageRes.empty()).build();
        }

        // 过滤掉无效的考试成员
        List<TenantMemberPvdRes> examMembers = this.tenantMemberIntegration.listByMemberId(examMemberIds);
        if (CollectionUtils.isEmpty(examMembers)) {
            return builder.page(BasePageRes.empty()).build();
        }

        // 保留有效的考试成员
        examMembers = examMembers.parallelStream().filter(new Predicate<TenantMemberPvdRes>() {
            @Override
            public boolean test(TenantMemberPvdRes entity) {
                return YesNoEnum.YES.getValue().equals(entity.getValidFlag());
            }
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(examMembers)) {
            return builder.page(BasePageRes.empty()).build();
        }

        // 有效的考试成员总人数
        builder.allCount(examMembers.size());
        // 保留有效考试学员
        List<Long> allExamMemberIds = examMembers.parallelStream().map(TenantMemberPvdRes::getId).collect(Collectors.toList());

        // 姓名或学号模糊搜索
        if (StringUtils.isNotEmpty(name)) {
            examMembers = examMembers.stream()
                    .filter(e -> e.getName().contains(name) || e.getNo().contains(name))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(examMembers)) {
                return builder.page(BasePageRes.empty()).build();
            }
        }
        // 考试成员id集合
        examMemberIds = examMembers.parallelStream().map(TenantMemberPvdRes::getId).collect(Collectors.toList());

        // 计算考试提交人数
        Integer submitCnt = this.examStudentAnswerDao.countSubmitCntByExamId(examId, allExamMemberIds);
        builder.submitCount(submitCnt);

        // 排序规则
        String orderRule;
        if (StringUtils.isEmpty(sortRule)) {
            // 已批阅 - 未批阅 - 未提交
            orderRule = "check_status DESC, submit_status DESC, submit_time DESC";
        } else {
            // 分数排序
            orderRule = "stu_score_desc".equals(sortRule) ? "score DESC" : "score ASC";
        }
        Page<Object> page = PageHelper.startPage(pageNo, pageSize, orderRule);
        List<ExamStudentAnswerEntity> studentAnswers = this.examStudentAnswerDao.selectByParams(examId, examMemberIds, status);
        if (CollectionUtils.isEmpty(studentAnswers)) {
            BasePageRes<ExamCheckAnswerListRes> resPage = BasePageRes.newBuilder(new ArrayList<ExamCheckAnswerListRes>())
                    .addTotal(page.getTotal())
                    .addPageSize(pageSize)
                    .addPageNo(pageNo)
                    .build();
            return builder.page(resPage).build();
        }

        // 考试试题
        List<ExamTopicEntity> examTopics = this.examTopicDao.getListByExamId(examId);
        // 客观题数量
        long objCnt = examTopics.parallelStream().filter(new Predicate<ExamTopicEntity>() {
            @Override
            public boolean test(ExamTopicEntity entity) {
                return TestTopicTypeEnum.getObjTopicTypes().contains(entity.getType());
            }
        }).count();
        // 主观题数量
        long subCnt = examTopics.parallelStream().filter(new Predicate<ExamTopicEntity>() {
            @Override
            public boolean test(ExamTopicEntity entity) {
                return TestTopicTypeEnum.getSubTopicTypes().contains(entity.getType());
            }
        }).count();

        // 有答题的记录
        List<ExamStudentAnswerEntity> submitStudentAnswers = studentAnswers.stream().filter(new Predicate<ExamStudentAnswerEntity>() {
            @Override
            public boolean test(ExamStudentAnswerEntity entity) {
                // 过滤掉无答题的记录
                return entity.getId() > 0;
            }
        }).collect(Collectors.toList());

        // 是否允许补足迟到时间
        Integer fillTimeStatus = examActivity.getFillTimeStatus();
        // 考试时长毫秒级
        Long timeLimitMills = timeLimit * 60 * 1000L;

        // 学生迟到时间map key -> 学生答题卡id， value -> 该考生的考试结束时间（1、允许补足迟到时间：进入考试时间 + 考试时长 2、不允许补足迟到时间：进入考试时间 + 考试时长 - 迟到时间）
        Map<Long, Long> examStuLateMillsMap = submitStudentAnswers.parallelStream().collect(Collectors.toMap(new Function<ExamStudentAnswerEntity, Long>() {
            @Override
            public Long apply(ExamStudentAnswerEntity entity) {
                return entity.getId();
            }
        }, new Function<ExamStudentAnswerEntity, Long>() {
            @Override
            public Long apply(ExamStudentAnswerEntity entity) {
                // 考生进入考试时间
                Long signTime = entity.getSignTime();

                // 如果是补考
                if (Objects.equals(NumberUtils.INTEGER_ONE, entity.getResitFlag())) {
                    if (signTime > NumberUtils.INTEGER_ZERO) {
                        return signTime + timeLimitMills;
                    }
                    return NumberUtils.LONG_ZERO;
                }

                // 如果允许补足迟到时间，考生考试结束时间为进入考试的时间 + 考试时长
                if (Objects.equals(NumberUtils.INTEGER_ONE, fillTimeStatus)) {
                    return signTime + timeLimitMills;
                }
                // 迟到的毫秒数
                long lateMills = signTime - startMills;
                // 如果不允许补足迟到时间
                return signTime + timeLimitMills - lateMills;
            }
        }));

        // key - 学生成员id; value - 成员信息
        Map<Long, TenantMemberPvdRes> memberInfoMap = examMembers.parallelStream().collect(Collectors.toMap(TenantMemberPvdRes::getId, Function.identity(), (a, b) -> a));

        List<ExamCheckAnswerListRes> resList = studentAnswers.stream().map(new Function<ExamStudentAnswerEntity, ExamCheckAnswerListRes>() {
            @Override
            public ExamCheckAnswerListRes apply(ExamStudentAnswerEntity e) {
                Long memberId = e.getMemberId();
                TenantMemberPvdRes memberPvdRes = memberInfoMap.get(memberId);
                Long answerId = e.getId();
                ExamCheckAnswerListRes.ExamCheckAnswerListResBuilder resBuilder = ExamCheckAnswerListRes.builder()
                        .countObj((int) objCnt)
                        .countSubj((int) subCnt)
                        .memberId(memberId)
                        .no(memberPvdRes.getExperienceDuration() > 0 ? "体验学员" : memberPvdRes.getNo())
                        .name(memberPvdRes.getName())
                        .expStuFlag(memberPvdRes.getExperienceDuration() > 0 ? YesNoEnum.YES.getValue() : YesNoEnum.NO.getValue());
                if (answerId == 0L) {
                    // 尚未参与考试
                    return resBuilder.build();
                } else {
                    // 参与考试
                    Long screenTime = e.getScreenTime();
                    Long m = screenTime / 60000L;
                    Long s = (screenTime % 60000L) / 1000L;
                    // 考生本次考试结束时间
                    Long endTime = examStuLateMillsMap.getOrDefault(e.getId(), NumberUtils.LONG_ZERO);
                    Long goBackSignTime = e.getGoBackSignTime() == null ? NumberUtils.LONG_ZERO : e.getGoBackSignTime();
                    // 是否补考标识
                    int resitFlag = ExamStatusEnum.PROCESS.getValue().equals(publishStatus) ? NumberUtils.INTEGER_MINUS_ONE : e.getResitFlag();
                    String screenTimeStr = "";
                    if (screenTime > NumberUtils.LONG_ZERO) {
                        screenTimeStr = String.format("%s分%s秒", m, s);
                    }
                    resBuilder.id(answerId)
                        .score(YesNoEnum.YES.getValue().equals(e.getCheckStatus()) ? e.getScore() : 0d)
                        // “未交_0","未批_1","已批_2”
                        .status(NumberUtils.INTEGER_ZERO.equals(e.getSubmitStatus())
                                ? NumberUtils.INTEGER_ZERO : NumberUtils.INTEGER_ZERO.equals(e.getCheckStatus())
                                ? NumberUtils.INTEGER_ONE : NumberUtils.INTEGER_TWO)
                        .submitTime(e.getSubmitTime())
                        .submitDate(NumberUtils.LONG_ZERO.equals(e.getSubmitTime()) ? null : new Date(e.getSubmitTime()))
                        .screenLimit(e.getScreenLimit())
                        .screenTime(screenTime)
                        .endTime(endTime)
                        .resitFlag(resitFlag)
                        .goBackFlag(e.getGoBackFlag())
                        .firstEnterTime(goBackSignTime > NumberUtils.LONG_ZERO ? goBackSignTime : e.getSignTime())
                        .firstEnterDate(goBackSignTime > NumberUtils.LONG_ZERO ? new Date(goBackSignTime) : new Date(e.getSignTime()))
                        .screenTimeStr(screenTimeStr);
                }
                return resBuilder.build();
            }
        }).collect(Collectors.toList());
        return builder.page(BasePageRes.newBuilder(resList)
                .addPageNo(pageNo)
                .addPageSize(pageSize)
                .addTotal(page.getTotal())
                .build()).build();
    }
}
