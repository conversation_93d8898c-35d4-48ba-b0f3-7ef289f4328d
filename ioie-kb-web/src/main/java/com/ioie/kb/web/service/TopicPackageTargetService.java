package com.ioie.kb.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ioie.kb.db.entity.TopicPackageTargetEntity;
import com.ioie.kb.ms.provider.req.TargetTopicSelectReq;
import com.ioie.kb.ms.provider.req.TargetTopicUpdateScoreReq;

import java.util.List;

/**
 * @description: some desc
 * @menu: some name
 * @author: lld
 * @date: 2021/6/10 11:33
 */
public interface TopicPackageTargetService extends IService<TopicPackageTargetEntity> {


    int updateBatch(List<TopicPackageTargetEntity> list);

    int updateBatchSelective(List<TopicPackageTargetEntity> list);

    int batchInsert(List<TopicPackageTargetEntity> list);

    int insertOrUpdate(TopicPackageTargetEntity record);

    int insertOrUpdateSelective(TopicPackageTargetEntity record);

    void addTopicPackageTarget(TargetTopicSelectReq targetTopicSelectReq);

    void addPackageTopic(TargetTopicSelectReq targetTopicSelectReq);

    void deleteTopicPackageTarget(TargetTopicSelectReq targetTopicSelectReq);

    void delPackageTopic(TargetTopicSelectReq targetTopicSelectReq);

    void updatePackageTopic(TargetTopicUpdateScoreReq targetTopicUpdateScoreReq);
}

