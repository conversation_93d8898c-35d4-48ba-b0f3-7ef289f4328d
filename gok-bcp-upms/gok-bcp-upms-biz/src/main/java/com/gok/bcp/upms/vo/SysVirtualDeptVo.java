package com.gok.bcp.upms.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * desc
 *
 * <AUTHOR> <PERSON>.<PERSON>
 * @version : 1.0
 * @since : 2023-10-11
 */
@Data
public class SysVirtualDeptVo implements Serializable{

    /**
     * 主键ID
     */
    private Long deptId;

    /**
     * 组织类型id
     */
    private Long deptCatId;

    /**
     * 编号
     */
    private String code;

    /**
     * 级别
     */
    private Integer level;


    /**
     * 部门名字
     */
    private String name;


    /**
     * 状态（0正常，8停用，9报废）
     */
    private String status;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;




}
