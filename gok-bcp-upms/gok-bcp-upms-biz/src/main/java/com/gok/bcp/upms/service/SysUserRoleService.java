/*
 *
 *      Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the pig4cloud.com developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: lengleng (<EMAIL>)
 *
 */

package com.gok.bcp.upms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gok.bcp.upms.entity.SysUserRole;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
public interface SysUserRoleService extends IService<SysUserRole> {

    /**
     * 根据用户Id删除该用户的角色关系
     *
     * @param userId 用户Id
     * @return Boolean
     */
    Boolean deleteByUserId(Long userId);

    /**
     * 根据角色id删除该角色的用户关系
     *
     * @param roleId 角色Id
     */
    void deleteByRoleId(Long roleId);

    /**
     * 删除单挑用户角色数据
     * @param roleId 角色Id
     * @param userId 用户ID
     */
    void deleteByUserRoleId(Long roleId,Long userId);


    /**
     * 根据角色ids查询用户ids
     *
     * @param ids 角色ids
     * @return list
     */
    List<Long> getUserIdListByRoleIds(List<Long> ids);


    /**
     * 根据角色ids查询用户
     *
     * @param ids 角色ids
     * @return list
     */
    Map<Long, Integer> getUserCountByRoleIds(List<Long> ids);

    /**
     * 按用户ID获取角色ID列表
     *
     * @param userId 用户ID
     * @return {@link List}<{@link Long}>
     */
    List<Long> getRoleIdListByUserId(Long userId);


    /**
     * 根据用户ids查询用户角色关联关系
     *
     * @param userId 用户id
     * @return list
     */
    List<SysUserRole> getListByUserId(Long userId);

    /**
     * 是否存在用户角色记录
     * @param userId 用户id
     * @param roleId 角色id
     * @return true/false
     */
    Boolean ifExistByUserIdAndRoleId(Long userId,Long roleId);
}
