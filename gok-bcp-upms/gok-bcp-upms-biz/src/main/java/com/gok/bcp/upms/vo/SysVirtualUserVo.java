package com.gok.bcp.upms.vo;

import com.gok.bcp.common.util.Format;
import com.gok.bcp.upms.common.enums.EmployeeStatusEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * desc
 *
 * <AUTHOR> <PERSON>.Ko
 * @version : 1.0
 * @since : 2023-10-11
 */
@Data
public class SysVirtualUserVo implements Serializable, Format {

    /**
     * 主键ID
     */
    private Long userId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 员工工号
     */
    private String staffId;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 登录账号
     */
    private String account;


    /**
     * 人员类型
     */
    private String memberType;

    /**
     * 人员状态
     */
    private String employeeStatus;

    /**
     * 是否在职(true/false/null)
     */
    private Boolean online;

    /**
     * 人员岗位
     */
    private String memberPosition;

    /**
     * 用户部门id
     */
    private Long userDeptId;

    /**
     * 部门全名
     */
    private String deptNameAll;


    @Override
    public void format() {
        //online
        if (StringUtils.isNotEmpty(employeeStatus)) {
            try {
                online = EmployeeStatusEnum.ifOnline(Integer.valueOf(employeeStatus));
            } catch (Exception ex) {
                //nothing to do
            }
        }
    }
}
