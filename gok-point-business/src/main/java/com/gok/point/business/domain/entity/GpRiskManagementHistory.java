package com.gok.point.business.domain.entity;

import com.gok.point.common.annotation.Excel;
import com.gok.point.common.core.domain.BaseEntity;
import lombok.*;

import java.time.LocalDate;

/**
 * 风险管理对象 gp_risk_management
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GpRiskManagementHistory extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 风险类型(0用户每日积分上限1用户评价次数限制2用户评价原因限制3友好建议次数限制4用户建议原因限制)
     */
    @Excel(name = "风险类型(0用户每日积分上限1用户评价次数限制2用户评价原因限制3友好建议次数限制4用户建议原因限制)")
    private Integer riskType;

    /**
     * 风险名
     */
    @Excel(name = "风险名")
    private String key;

    /**
     * 风险值
     */
    @Excel(name = "风险值")
    private String value;
    /**
     * 创建日期
     */
    private LocalDate createDate;
}

