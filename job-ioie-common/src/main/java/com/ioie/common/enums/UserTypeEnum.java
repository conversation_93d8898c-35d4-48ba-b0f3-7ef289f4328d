package com.ioie.common.enums;

import java.util.Arrays;

/**
 * 用户类型
 *
 * <AUTHOR>
 * @date 2020年08月27日
 */
public enum UserTypeEnum {

    /**
     * 通用
     */
    NORMAL(1),
    /**
     * 租户成员
     */
    TENANT_MEMBER(2),
    /**
     * 运营人员
     */
    OPERATOR(3);

    private int value;

    UserTypeEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static UserTypeEnum getEnumByValue(Integer value) {
        if (null == value) {
            return null;
        }
        return Arrays.stream(values())
                .filter(type -> type.value == value)
                .findFirst()
                .orElse(null);
    }
}
