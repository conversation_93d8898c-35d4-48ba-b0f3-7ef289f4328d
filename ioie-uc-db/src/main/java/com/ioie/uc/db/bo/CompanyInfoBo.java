package com.ioie.uc.db.bo;

import lombok.Builder;
import lombok.Data;

/**
    * 公司基础信息表
    */
@Data
@Builder
public class CompanyInfoBo  {

    /**
     * id
     */
    private Long id;


    /**
     * 企业名称
     */
    private String name;

    /**
     * 公司简称
     */
    private String shortName;

    /**
     * 联系人
     */
    private String contactMan;

    /**
     * 电话号码
     */
    private String telephone;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 区/镇
     */
    private String area;

    /**
     * 注册地址
     */
    private String signAddress;

    /**
     * 详细地址
     */
    private String address;

    /**
     * logo图片地址
     */
    private String logoUrl;

    /**
     * 营业执照图片地址
     */
    private String licenseUrl;

    /**
     * 公司邮箱
     */
    private String email;

    /**
     * 公司来源：后端管理, 集客服务, 国科就业
     */
    private Integer source;

    /**
     * 行业类型
     */
    private String industryType;

    /**
     * 行业类型 字典value
     */
    private Long industryTypId;



    /**
     * 公司规模:0-未选
     */
    private Integer scale;

    /**
     * 公司规模:0-未选
     */
    private Integer minScale;

    /**
     * 公司规模: 数据字典
     */
    private Long companySize;

    /**
     * 是否开启首页推荐:0-否;1-是
     */
    private Integer homeRecommendFlag;

    /**
     * 是否开启首页推荐:0-否;1-是
     */
    private Long homeRecommendTime;


    /**
     * 是否开启招聘权限:0-否;1-是
     */
    private Integer takeJobFlag;

    /**
     * 是否开启职位审核:0-否;1-是
     */
    private Integer auditCareerFlag;

    /**
     * 是否开启简历审核:0-否;1-是
     */
    private Integer auditResumeFlag;

    /**
     * 公司介绍
     */
    private String introduce;

    /**
     * 公司简评
     */
    private String remark;

    /**
     * 租户有效性 0:无效，1:有效
     */
    private Integer validFlag;

    /**
     * 公司福利
     */
    private String welfare;

    /**
     * 租户id
     */
    private Long tenantId;


}