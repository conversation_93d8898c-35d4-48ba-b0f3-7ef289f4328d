package com.gok.pboot.pms.handler.perm.impl;

import cn.hutool.core.collection.CollUtil;
import com.gok.pboot.common.security.util.SecurityUtils;
import com.gok.pboot.pms.Util.BaseEntityUtils;
import com.gok.pboot.pms.Util.EnumUtils;
import com.gok.pboot.pms.common.base.BaseEntity;
import com.gok.pboot.pms.common.constant.EntitySign;
import com.gok.pboot.pms.entity.bo.TaskReviewerInfoBO;
import com.gok.pboot.pms.entity.domain.*;
import com.gok.pboot.pms.entity.dto.CustomerBusinessDTO;
import com.gok.pboot.pms.entity.dto.CustomerBusinessUnitPageDTO;
import com.gok.pboot.pms.enumeration.BusinessProjectStatusEnum;
import com.gok.pboot.pms.enumeration.ProjectTaskRoleEnum;
import com.gok.pboot.pms.enumeration.RoleTypeEnum;
import com.gok.pboot.pms.enumeration.TaskReviewerTypeEnum;
import com.gok.pboot.pms.eval.entity.domain.EvalUserRole;
import com.gok.pboot.pms.eval.enums.EvalUserRoleEnum;
import com.gok.pboot.pms.eval.service.IEvalUserRoleService;
import com.gok.pboot.pms.handler.ProjectScopeHandle;
import com.gok.pboot.pms.handler.perm.PmsRetriever;
import com.gok.pboot.pms.mapper.*;
import com.google.common.collect.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * PMS主数据检索器
 *
 * <AUTHOR>
 * @version 1.3.2
 */
@Component
@RequiredArgsConstructor
public class PmsMasterRetriever implements PmsRetriever {


    private final ProjectInfoMapper projectInfoMapper;

    private final BusinessInfoMapper businessInfoMapper;

    private final ProjectTaskeMapper projectTaskeMapper;

    private final ProjectTaskeUserMapper projectTaskeUserMapper;

    private final ProjectStakeholderMemberMapper projectStakeholderMemberMapper;

    private final RosterMapper rosterMapper;

    private final EntityOptionMapper entityOptionMapper;

    private final ProjectScopeHandle projectScopeHandle;

    private final CustomerBusinessPersonMapper customerBusinessPersonMapper;

    private final CustomerBusinessUnitMapper customerBusinessUnitMapper;

    private final IEvalUserRoleService evalUserRoleService;


    @Override
    public List<Long> getBusinessIdsAvailable(CustomerBusinessDTO dto) {
        Map<String, Object> filter = assembleFilter();
        String scope = (String) filter.get(SCOPE);
        if (ALL.equals(scope)) {
            dto.setScope(scope);
        }
        List<Long> userIdList = (List<Long>) filter.get("userIdList");
        if (!ALL.equals(scope) && CollUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        List<Long> findBusinessIdByPerson = customerBusinessPersonMapper.findId(filter);
        List<Long> findBusinessIdByUnit = customerBusinessUnitMapper.findId(filter);
        List<Long> businessIdList = new ArrayList<>();
        businessIdList.addAll(findBusinessIdByPerson);
        businessIdList.addAll(findBusinessIdByUnit);
        return businessIdList;
    }

    @Override
    public List<Long> getBusinessIdsAvailable(CustomerBusinessUnitPageDTO dto) {
        Map<String, Object> filter = assembleFilter();
        String scope = (String) filter.get(SCOPE);
        if (ALL.equals(scope)) {
            dto.setScope(scope);
        }
        List<Long> userIdList = (List<Long>) filter.get("userIdList");
        if (!ALL.equals(scope) && CollUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        List<Long> findBusinessIdByPerson = customerBusinessPersonMapper.findId(filter);
        List<Long> findBusinessIdByUnit = customerBusinessUnitMapper.findId(filter);
        List<Long> businessIdList = new ArrayList<>();
        businessIdList.addAll(findBusinessIdByPerson);
        businessIdList.addAll(findBusinessIdByUnit);
        return businessIdList;
    }

    @Override
    public List<Long> getProjectIdsAvailable() {
        Map<String, Object> filter = assembleFilter();
        String scope = (String) filter.get(SCOPE);
        List<Long> userIdList = (List<Long>) filter.get("userIdList");
        if (!ALL.equals(scope) && CollUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        return projectInfoMapper.findId(filter);
    }

    /**
     * 获取用户数据权限
     *
     * @param filter 过滤条件
     * @return 项目ID集合
     */
    @Override
    public List<Long> getProjectIdsAvailable(Map<String, Object> filter) {
        Map<String, Object> authFilter = assembleFilter();
        String scope = (String) authFilter.get(SCOPE);
        List<Long> userIdList = (List<Long>) authFilter.get("userIdList");
        if (ALL.equals(scope)) {
            filter.put(SCOPE, ALL);
            return new ArrayList<>();
        } else if (CollUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        return projectInfoMapper.findId(authFilter);
    }

    /**
     * 获取用户数据权限
     *
     * @param filter 过滤条件
     * @return 商机ID集合
     */
    @Override
    public List<Long> getBusinessIdsAvailable(Map<String, Object> filter) {
        Map<String, Object> authFilter = assembleFilter();
        String scope = (String) authFilter.get(SCOPE);
        List<Long> userIdList = (List<Long>) authFilter.get("userIdList");
        if (ALL.equals(scope)) {
            filter.put(SCOPE, ALL);
            return new ArrayList<>();
        } else if (CollUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        return businessInfoMapper.findId(authFilter);
    }

    @Override
    public void calcProjectIdsAvailable(Map<String, Object> filter) {
        filter.put(PROJECT_AVAILABLE_KEY, getProjectIdsAvailable());
    }

    @Override
    public boolean isProjectAvailable(Long projectId) {
        return getProjectIdsAvailable().contains(projectId);
    }

    private Map<String, Object> assembleFilter() {
        Map<String, Object> filter = Maps.newHashMap();

        projectScopeHandle.calcScope(filter);

        return filter;
    }

    @Override
    public boolean isTaskAvailable(Long taskId) {
        Long projectId = projectTaskeMapper.findProjectIdByTaskId(taskId);
        List<ProjectStakeholderMember> projectMembers;
        Long userId;

        if (projectId == null) {
            return false;
        }
        userId = SecurityUtils.getUser().getId();
        projectMembers = projectStakeholderMemberMapper.findByProjectIdAndMemberId(projectId, userId);
        if (projectMembers.isEmpty()) {
            return false;
        }
        if (projectMembers.stream().allMatch(pm -> EnumUtils.valueEquals(pm.getRoleType(), RoleTypeEnum.PROJECT_MEMBER))) {
            // 当前人员仅为项目成员时，如果不在项目任务中，则不允许访问
            return !projectTaskeUserMapper.findByUserId(userId).isEmpty();
        }

        return true;
    }

    @Override
    public TaskReviewerInfoBO getReviewerInfo(Long userId, Long taskId) {
        List<ProjectTaskeUser> taskLeaders = projectTaskeUserMapper.findListByTaskId(taskId, ProjectTaskRoleEnum.LEADER.getValue());
        Long projectId = projectTaskeMapper.findProjectIdByTaskId(taskId);
        boolean isSpecialProject = entityOptionMapper.existsByEntityIdAndSign(projectId, EntitySign.SPECIAL_PROJECT);
        ProjectInfo project;
        Roster roster = rosterMapper.selectById(userId);

        if (projectId == null || taskLeaders.isEmpty() || roster == null) {
            return TaskReviewerInfoBO.empty();
        }
        project = projectInfoMapper.selectById(projectId);

        return createTaskReviewerInfoBO(roster, project, taskLeaders, isSpecialProject);
    }

    @Override
    public Map<Long, TaskReviewerInfoBO> getReviewerInfo(Long userId, Collection<Long> taskIds) {
        Map<Long, List<ProjectTaskeUser>> taskIdAndLeadersMap = projectTaskeUserMapper.findByTaskIdsAndTaskRole(taskIds, ProjectTaskRoleEnum.LEADER.getValue()).stream().collect(Collectors.groupingBy(ProjectTaskeUser::getTaskId));
        Map<Long, Long> taskIdAndProjectIdMap;
        Set<Long> specialProjectIds;
        Map<Long, Boolean> taskIdIdAndSpecialProjectFlagMap;
        Map<Long, ProjectInfo> projectIdAndInfoMap;
        Roster roster;

        if (taskIdAndLeadersMap.isEmpty()) {
            return ImmutableMap.of();
        }
        taskIdAndProjectIdMap = projectTaskeMapper.findByTaskIds(taskIds).stream().collect(Collectors.toMap(BaseEntity::getId, ProjectTaske::getProjectId));
        if (taskIdAndProjectIdMap.isEmpty()) {
            return ImmutableMap.of();
        }
        projectIdAndInfoMap = BaseEntityUtils.mapCollectionToIdMap(projectInfoMapper.selectBatchIds(taskIdAndProjectIdMap.values()));
        if (projectIdAndInfoMap.isEmpty()) {
            return ImmutableMap.of();
        }
        specialProjectIds = entityOptionMapper.findIdSetBySign(EntitySign.SPECIAL_PROJECT);
        taskIdIdAndSpecialProjectFlagMap = taskIdAndProjectIdMap.keySet().stream().collect(Collectors.toMap(taskId -> taskId, taskId -> specialProjectIds.contains(taskIdAndProjectIdMap.getOrDefault(taskId, 0L))));
        roster = rosterMapper.selectById(userId);
        if (roster == null) {
            return ImmutableMap.of();
        }

        return taskIdAndProjectIdMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> {
            Long taskId = entry.getKey();
            Long projectId = entry.getValue();
            List<ProjectTaskeUser> leaders = taskIdAndLeadersMap.getOrDefault(taskId, ImmutableList.of());
            boolean isSpecialProject = taskIdIdAndSpecialProjectFlagMap.getOrDefault(taskId, false);
            ProjectInfo project = projectIdAndInfoMap.get(projectId);

            return createTaskReviewerInfoBO(roster, project, leaders, isSpecialProject);
        }));
    }

    @Override
    public Table<Long, Long, TaskReviewerInfoBO> getReviewerInfo(Collection<Pair<Long, Long>> userIdAndTaskIdPairs) {
        if (userIdAndTaskIdPairs.isEmpty()) {
            return ImmutableTable.of();
        }

        int paramSize = userIdAndTaskIdPairs.size();
        Set<Long> userIds = Sets.newHashSetWithExpectedSize(paramSize);
        Set<Long> taskIds = Sets.newHashSetWithExpectedSize(paramSize);
        Map<Long, List<ProjectTaskeUser>> taskIdAndLeadersMap;
        Map<Long, Long> taskIdAndProjectIdMap;
        Set<Long> specialProjectIds;
        Map<Long, Boolean> taskIdIdAndSpecialProjectFlagMap;
        Map<Long, ProjectInfo> projectIdAndInfoMap;
        Map<Long, Roster> userIdAndRosterMap;
        Table<Long, Long, TaskReviewerInfoBO> result;

        userIdAndTaskIdPairs.forEach(pair -> {
            userIds.add(pair.getFirst());
            taskIds.add(pair.getSecond());
        });
        taskIdAndLeadersMap = projectTaskeUserMapper.findByTaskIdsAndTaskRole(taskIds, ProjectTaskRoleEnum.LEADER.getValue()).stream().collect(Collectors.groupingBy(ProjectTaskeUser::getTaskId));
        if (taskIdAndLeadersMap.isEmpty()) {
            return ImmutableTable.of();
        }
        taskIdAndProjectIdMap = projectTaskeMapper.findByTaskIds(taskIds).stream().collect(Collectors.toMap(BaseEntity::getId, ProjectTaske::getProjectId));
        if (taskIdAndProjectIdMap.isEmpty()) {
            return ImmutableTable.of();
        }
        projectIdAndInfoMap = BaseEntityUtils.mapCollectionToIdMap(projectInfoMapper.selectBatchIds(taskIdAndProjectIdMap.values()));
        if (projectIdAndInfoMap.isEmpty()) {
            return ImmutableTable.of();
        }
        specialProjectIds = entityOptionMapper.findIdSetBySign(EntitySign.SPECIAL_PROJECT);
        taskIdIdAndSpecialProjectFlagMap = taskIdAndProjectIdMap.keySet().stream().collect(Collectors.toMap(taskId -> taskId, taskId -> specialProjectIds.contains(taskIdAndProjectIdMap.getOrDefault(taskId, 0L))));
        userIdAndRosterMap = rosterMapper.findUserIdMap(userIds);
        if (userIdAndRosterMap.isEmpty()) {
            return ImmutableTable.of();
        }
        result = HashBasedTable.create(paramSize, paramSize);
        userIdAndTaskIdPairs.forEach(pair -> {
            Long userId = pair.getFirst();
            Long taskId = pair.getSecond();
            Roster roster = userIdAndRosterMap.get(userId);
            Long projectId;
            List<ProjectTaskeUser> leaders;
            boolean isSpecialProject;
            ProjectInfo project;

            if (roster == null) {
                return;
            }
            projectId = taskIdAndProjectIdMap.get(taskId);
            if (projectId == null) {
                return;
            }
            leaders = taskIdAndLeadersMap.getOrDefault(taskId, ImmutableList.of());
            isSpecialProject = taskIdIdAndSpecialProjectFlagMap.getOrDefault(taskId, false);
            project = projectIdAndInfoMap.get(projectId);
            result.put(userId, taskId, createTaskReviewerInfoBO(roster, project, leaders, isSpecialProject));
        });

        return result;
    }

    /**
     * 获取项目 ID 范围
     * -总支撑管可以看到自己团队下的所有项目。
     * -项目经理、客户经理可以看自己负责的项目
     * -PMO可以看到全部
     *
     * @param filter          filter
     * @param functionsLeader 是否包含职能主管
     * @return {@link List }<{@link Long }>
     */
    @Override
    public List<Long> getProjectIdScope(Map<String, Object> filter, boolean functionsLeader) {
        Long userId = SecurityUtils.getUser().getId();
        List<Integer> userRoleValues = CollUtil.newArrayList(EvalUserRoleEnum.PMO.getValue());
        if (functionsLeader) {
            userRoleValues.add(EvalUserRoleEnum.CUSTOMER_MARKET_LEADER.getValue());
            userRoleValues.add(EvalUserRoleEnum.SHARED_OPERATION_LEADER.getValue());
        }
        boolean isLeader = evalUserRoleService.lambdaQuery()
                .in(EvalUserRole::getRole, userRoleValues)
                .eq(EvalUserRole::getUserId, userId)
                .exists();
        if (isLeader) {
            filter.put(SCOPE, ALL);
            return Collections.emptyList();
        }
        return projectInfoMapper.getProjectIdScope(userId);
    }

    /**
     * 根据用户和任务信息，创建任务主责审核人信息
     *
     * @param roster           用户信息
     * @param project          项目
     * @param taskLeaders      任务负责人列表
     * @param isSpecialProject 是否特殊项目
     * @return 任务主责审核人信息
     */
    private TaskReviewerInfoBO createTaskReviewerInfoBO(Roster roster, ProjectInfo project, Collection<ProjectTaskeUser> taskLeaders, boolean isSpecialProject) {
        TaskReviewerInfoBO result = new TaskReviewerInfoBO();
        Long userId = roster.getId();
        Long managerId;
        Long salesmanId;
        Long preSalesmanId;
        Integer projectStatus;
        // 用户是否为任务负责人
        boolean isTaskLeader = taskLeaders.stream().anyMatch(tl -> userId.equals(tl.getUserId()));
        // 用户是项目经理
        boolean isProjectManager;
        // 用户是销售
        boolean isSalesman;
        // 用户是售前
        boolean isPreSalesman;

        Set<TaskReviewerTypeEnum> types = Sets.newHashSetWithExpectedSize(3);

        managerId = project.getManagerUserId();
        isProjectManager = userId.equals(managerId);
        salesmanId = project.getSalesmanUserId();
        isSalesman = userId.equals(salesmanId);
        preSalesmanId = project.getPreSaleUserId();
        isPreSalesman = userId.equals(preSalesmanId);
        if (isSpecialProject) {
            // 如果是特殊项目，直接上级主核
            types.add(TaskReviewerTypeEnum.DIRECT_LEADER);
            result.setDirectLeaderUserId(roster.getLeaderId());
            if (isProjectManager || isSalesman || isPreSalesman) {
                // 如果是特殊项目，且当前用户是铁三角，铁三角可以额外主审
                types.add(TaskReviewerTypeEnum.IRON_TRIANGLE);
                result.setProjectManagerUserId(managerId);
                if (project.getIsNotInternalProject() == 2) {
                    result.setProjectSalesmanUserId(salesmanId);
                    result.setProjectPreSalesmanUserId(preSalesmanId);
                }
            }
            result.setTypes(types);

            return result;
        }
        if (!isTaskLeader) {
            // 如果用户不是任务负责人，则由除该用户之外的任务负责人审核
            if (!taskLeaders.isEmpty()) {
                types.add(TaskReviewerTypeEnum.TASK_LEADER);
                result.setTaskLeaderUserIds(taskLeaders.stream().map(ProjectTaskeUser::getUserId).collect(Collectors.toList()));
                result.setTypes(types);

                return result;
            }
        }
        // 如果用户是任务负责人，尝试走铁三角审核
        if (isProjectManager || isSalesman || isPreSalesman) {
            // 用户是任务负责人，同时是铁三角，由其直接上级审核
            types.add(TaskReviewerTypeEnum.DIRECT_LEADER);
            result.setTypes(types);
            result.setDirectLeaderUserId(roster.getLeaderId());

        } else {
            // 用户是任务负责人，不是铁三角，由其直接上级审核
            projectStatus = NumberUtils.toInt(project.getProjectStatus(), Integer.MAX_VALUE);
            types.add(TaskReviewerTypeEnum.IRON_TRIANGLE);
            result.setTypes(types);
            if (EnumUtils.valueEquals(projectStatus, BusinessProjectStatusEnum.SJ) || EnumUtils.valueEquals(projectStatus, BusinessProjectStatusEnum.SJZZ)) {
                if (project.getIsNotInternalProject() == 2) {
                    result.setProjectSalesmanUserId(salesmanId);
                    result.setProjectPreSalesmanUserId(preSalesmanId);
                }
            }
            result.setProjectManagerUserId(managerId);
        }

        return result;
    }
}
