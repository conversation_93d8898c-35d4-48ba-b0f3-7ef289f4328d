package com.gok.pboot.common.core.validation.annotation;

import com.gok.pboot.common.core.validation.validator.StringValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * 字符串验证
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Target({ElementType.METHOD, ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {StringValidator.class})
public @interface StringVerify {

    String message() default "参数不符合要求";

    String name();

    boolean required() default false;

    int minLen() default 1;

    int maxLen() default Integer.MAX_VALUE;

    String[] valueRanges() default {};

    String regexp() default "";

    String[] contains() default {};

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
