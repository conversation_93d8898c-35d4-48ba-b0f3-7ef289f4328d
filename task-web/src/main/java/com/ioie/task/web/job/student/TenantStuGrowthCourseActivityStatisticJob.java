package com.ioie.task.web.job.student;

import com.ioie.task.db.bo.GrowthCourseUserBo;
import com.ioie.task.db.bo.MemberActivityNumBo;
import com.ioie.task.db.dao.statistic.TenantStuGrowthCourseActivityStatisticDao;
import com.ioie.task.db.dao.tac.GrowthCourseTeachActivityDao;
import com.ioie.task.db.dao.tac.SelfLearningUserDao;
import com.ioie.task.db.dao.tac.TeachActivityDao;
import com.ioie.task.db.entity.statistic.TenantStuGrowthCourseActivityStatistic;
import com.ioie.task.web.constant.JobType;
import com.ioie.task.web.constant.TenantStuActivityType;
import com.ioie.task.web.job.AbstractJobService;
import com.ioie.web.utils.BeanHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import groovy.util.logging.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TenantStuGrowthCourseActivityStatisticJob extends AbstractJobService {

    @Resource
    private TenantStuGrowthCourseActivityStatisticDao tenantStuGrowthCourseActivityStatisticDao;

    @Resource
    private SelfLearningUserDao selfLearningUserDao;

    @Resource
    private TeachActivityDao teachActivityDao;

    @Resource
    private GrowthCourseTeachActivityDao growthCourseTeachActivityDao;


    @XxlJob("tTenantStuGrowthCourseActivityStatisticJob")
    public ReturnT<String> teaStuHomeworkJob() {
        return super.executeJob();
    }

    @Override
    protected JobType jobType() {
        return JobType.TENANT_STU_GROWTH_COURSE_ACTIVITY_STATISTIC_JOB;
    }

    @Override
    protected void handleJob() {
        //有效的学生
        List<GrowthCourseUserBo> growthCourseUserBos = selfLearningUserDao.selectGrowthUsers();
        //有效的班课
        List<Long> classIds = growthCourseUserBos.parallelStream().map(GrowthCourseUserBo::getClassId).collect(Collectors.toList());

        List<TenantStuGrowthCourseActivityStatistic> allList = new ArrayList<>();

        // 获取学生问卷数据，实际提交数据
        List<MemberActivityNumBo> questAttends = this.teachActivityDao.selectStuQuestAttendAllGrowthCourse(classIds);
        if (CollectionUtils.isNotEmpty(questAttends)) {
            //按成员id、租户id、班课id分组获取统计数据
            Set<Map.Entry<String, List<MemberActivityNumBo>>> questClassEntry = questAttends.parallelStream()
                    .collect(Collectors.groupingBy(a -> a.getMemberId().toString() + a.getTenantId().toString() + a.getClassId().toString()))
                    .entrySet();
            List<TenantStuGrowthCourseActivityStatistic> questClassCntList = questClassEntry.parallelStream().map(entry -> {
                List<MemberActivityNumBo> memberActivityNumBoList = entry.getValue();
                MemberActivityNumBo memberQuestNumBo = memberActivityNumBoList.get(0);
                return TenantStuGrowthCourseActivityStatistic.builder()
                        .tenantId(memberQuestNumBo.getTenantId())
                        .growthClassId(memberQuestNumBo.getClassId())
                        .memberId(memberQuestNumBo.getMemberId())
                        .attend(memberActivityNumBoList.size())
                        .type(TenantStuActivityType.QUEST.getValue())
                        .build();
            }).collect(Collectors.toList());
            allList.addAll(questClassCntList);
        }

        // 获取学生问卷数据，应提交数据
        List<MemberActivityNumBo> questNeeds = this.growthCourseTeachActivityDao.selectGrowthCourseStuQuestNeedJob(classIds);
        if (CollectionUtils.isNotEmpty(questNeeds)) {
            //按成员id、租户id、班课id分组获取统计数据
            Set<Map.Entry<String, List<MemberActivityNumBo>>> questClassNeedEntry = questNeeds.parallelStream()
                    .collect(Collectors.groupingBy(a -> a.getMemberId().toString() + a.getTenantId().toString() + a.getClassId().toString()))
                    .entrySet();
            List<TenantStuGrowthCourseActivityStatistic> questClassNeedCntList = questClassNeedEntry.parallelStream().map(entry -> {
                List<MemberActivityNumBo> memberActivityNumBoList = entry.getValue();
                MemberActivityNumBo memberQuestNumBo = memberActivityNumBoList.get(0);
                return TenantStuGrowthCourseActivityStatistic.builder()
                        .tenantId(memberQuestNumBo.getTenantId())
                        .growthClassId(memberQuestNumBo.getClassId())
                        .memberId(memberQuestNumBo.getMemberId())
                        .need(memberActivityNumBoList.size())
                        .type(TenantStuActivityType.QUEST.getValue())
                        .build();
            }).collect(Collectors.toList());
            allList.addAll(questClassNeedCntList);

        }

        // 学生测试实际提交数
        List<MemberActivityNumBo> testAllList = this.teachActivityDao.stuTestAttendAllGrowthCourse(classIds);
        if (CollectionUtils.isNotEmpty(testAllList)) {
            // 学生测试提交数  分组条件 memberId 、 tenantId 、 classId
            Set<Map.Entry<String, List<MemberActivityNumBo>>> testClassEntry = testAllList.parallelStream()
                    .collect(Collectors.groupingBy(a -> a.getMemberId().toString() + a.getTenantId().toString() + a.getClassId()))
                    .entrySet();
            List<TenantStuGrowthCourseActivityStatistic> testClassAttendList = testClassEntry.parallelStream()
                    .map(entry -> {
                            List<MemberActivityNumBo> memberActivityNumBoList = entry.getValue();
                        MemberActivityNumBo memberTestNumBo = memberActivityNumBoList.get(0);
                        return TenantStuGrowthCourseActivityStatistic.builder()
                                    .tenantId(memberTestNumBo.getTenantId())
                                    .growthClassId(memberTestNumBo.getClassId())
                                    .memberId(memberTestNumBo.getMemberId())
                                    .attend(memberActivityNumBoList.size())
                                    .type(TenantStuActivityType.TEST.getValue())
                                    .build();
            }).collect(Collectors.toList());
            allList.addAll(testClassAttendList);
        }

        // 学生测试应提交数
        List<MemberActivityNumBo> testAllNeeds = this.growthCourseTeachActivityDao.selectGrowthCourseStuTestNeedJob(classIds);
        if (CollectionUtils.isNotEmpty(testAllNeeds)) {
            // 学生测试应提交数  分组条件 memberId 、 tenantId 、 classId
            Set<Map.Entry<String, List<MemberActivityNumBo>>> testNeedEntry = testAllNeeds.parallelStream()
                    .collect(Collectors.groupingBy(a -> a.getMemberId().toString() + a.getTenantId().toString() + a.getClassId().toString()))
                    .entrySet();

            List<TenantStuGrowthCourseActivityStatistic> testClassNeedList = testNeedEntry.parallelStream().map(entry -> {
                List<MemberActivityNumBo> memberActivityNumBoList = entry.getValue();
                MemberActivityNumBo memberTestNeedBo = memberActivityNumBoList.get(0);
                return TenantStuGrowthCourseActivityStatistic.builder()
                        .tenantId(memberTestNeedBo.getTenantId())
                        .growthClassId(memberTestNeedBo.getClassId())
                        .memberId(memberTestNeedBo.getMemberId())
                        .need(memberActivityNumBoList.size())
                        .type(TenantStuActivityType.TEST.getValue())
                        .build();
            }).collect(Collectors.toList());
            allList.addAll(testClassNeedList);
        }

        // 头脑风暴学生应提交数据
        List<MemberActivityNumBo> brainAllNeeds = growthCourseTeachActivityDao.selectGrowthCourseStuBrainNeedJob(classIds);
        if (CollectionUtils.isNotEmpty(brainAllNeeds)) {
            // 学生测试应提交数  分组条件 memberId 、 tenantId 、 classId
            Set<Map.Entry<String, List<MemberActivityNumBo>>> brainNeedEntry = brainAllNeeds.parallelStream()
                    .collect(Collectors.groupingBy(a -> a.getMemberId().toString() + a.getTenantId().toString() + a.getClassId().toString()))
                    .entrySet();

            List<TenantStuGrowthCourseActivityStatistic> brainNeedList = brainNeedEntry.parallelStream()
                    .map(entry -> {
                        List<MemberActivityNumBo> memberActivityNumBoList = entry.getValue();
                        MemberActivityNumBo memberActivityNumBo = memberActivityNumBoList.get(0);
                        return TenantStuGrowthCourseActivityStatistic.builder()
                                    .tenantId(memberActivityNumBo.getTenantId())
                                    .growthClassId(memberActivityNumBo.getClassId())
                                    .memberId(memberActivityNumBo.getMemberId())
                                    .need(memberActivityNumBoList.size())
                                    .type(TenantStuActivityType.BRAIN.getValue())
                                    .build();
            }).collect(Collectors.toList());
            allList.addAll(brainNeedList);
        }

        // 获取头脑风暴 学生提交数据
        List<MemberActivityNumBo> stuTenantStormAttendJobBoList = teachActivityDao.stuTenantStormAttendAllGrowthCourse(classIds);
        // 组装数据
        if (CollectionUtils.isNotEmpty(stuTenantStormAttendJobBoList)) {
            Set<Map.Entry<String, List<MemberActivityNumBo>>> brainClassEntry = stuTenantStormAttendJobBoList.parallelStream()
                    .collect(Collectors.groupingBy(a -> a.getMemberId().toString() + a.getTenantId().toString() + a.getClassId()))
                    .entrySet();
            List<TenantStuGrowthCourseActivityStatistic> brainAttendList = brainClassEntry.parallelStream()
                    .map(entry -> {
                        List<MemberActivityNumBo> memberActivityNumBoList = entry.getValue();
                        MemberActivityNumBo memberTestNumBo = memberActivityNumBoList.get(0);
                        return TenantStuGrowthCourseActivityStatistic.builder()
                                .tenantId(memberTestNumBo.getTenantId())
                                .growthClassId(memberTestNumBo.getClassId())
                                .memberId(memberTestNumBo.getMemberId())
                                .attend(memberActivityNumBoList.size())
                                .type(TenantStuActivityType.BRAIN.getValue())
                                .build();
                    }).collect(Collectors.toList());
            allList.addAll(brainAttendList);
        }


        // 学生练习实际提交数
        List<MemberActivityNumBo> practiceAllList = this.teachActivityDao.stuGrowthPracticeAttendAllJobBoList(classIds);
        if(CollectionUtils.isNotEmpty(practiceAllList)){
            // 学生练习提交数  分组条件 memberId 、 tenantId 、 classId
            List<TenantStuGrowthCourseActivityStatistic> practiceClassAttendList = practiceAllList.parallelStream()
                    .collect(Collectors.groupingBy(a -> a.getDataId().toString() + a.getMemberId().toString() + a.getTenantId().toString() + a.getClassId().toString() ))
                    .entrySet()
                    .parallelStream()
                    .map(entry -> {
                        List<MemberActivityNumBo> practiceInfoBos = entry.getValue();
                        MemberActivityNumBo practiceInfoBo = practiceInfoBos.get(NumberUtils.INTEGER_ZERO);
                        return TenantStuGrowthCourseActivityStatistic.builder()
                                .tenantId(practiceInfoBo.getTenantId())
                                .growthClassId(practiceInfoBo.getClassId())
                                .memberId(practiceInfoBo.getMemberId())
                                .attend(practiceInfoBos.size())
                                .type(TenantStuActivityType.PRACTICE.getValue())
                                .build();
                    })
                    .collect(Collectors.toList());
            allList.addAll(practiceClassAttendList);
        }

        // 学生练习应提交数
        List<MemberActivityNumBo> practiceAllNeeds = this.teachActivityDao.stuPracticeNeedGrowthJobBoList(classIds);
        if(CollectionUtils.isNotEmpty(practiceAllNeeds)){
            // 学生练习提交数  分组条件 memberId 、 tenantId 、 classId
            List<TenantStuGrowthCourseActivityStatistic> practiceClassNeedList = practiceAllNeeds.parallelStream()
                    .collect(Collectors.groupingBy(a -> a.getMemberId().toString() + a.getTenantId().toString() + a.getClassId().toString() ))
                    .entrySet()
                    .parallelStream()
                    .map(entry -> {
                        List<MemberActivityNumBo> practiceInfoBos = entry.getValue();
                        MemberActivityNumBo practiceInfoBo = practiceInfoBos.get(NumberUtils.INTEGER_ZERO);
                        return TenantStuGrowthCourseActivityStatistic.builder()
                                .tenantId(practiceInfoBo.getTenantId())
                                .growthClassId(practiceInfoBo.getClassId())
                                .memberId(practiceInfoBo.getMemberId())
                                .need(practiceInfoBos.size())
                                .type(TenantStuActivityType.PRACTICE.getValue())
                                .build();
                    })
                    .collect(Collectors.toList());
            allList.addAll(practiceClassNeedList);
        }

        // 录入数据
        if (CollectionUtils.isNotEmpty(allList)) {

            // 按照 key值分组 tenant_id`,`type`,`class_id`,`member_id
            Map<String, List<TenantStuGrowthCourseActivityStatistic>> uniqueKeyMap = allList.parallelStream()
                    .collect(Collectors.groupingBy(a -> a.getTenantId() + ":"  + a.getType() + ":"  + a.getGrowthClassId() + ":"  + a.getMemberId()));

            List<TenantStuGrowthCourseActivityStatistic> insertList = uniqueKeyMap.entrySet()
                    .stream()
                    .map(entry -> {
                        List<TenantStuGrowthCourseActivityStatistic> tenantStuActivityStatisticList = entry.getValue();
                        // 活动实际参与数
                        int attendNum = 0;
                        // 应参与数
                        int needNum = 0;

                        // 获取实际参与数
                        List<TenantStuGrowthCourseActivityStatistic> attendList = tenantStuActivityStatisticList.parallelStream().filter(e -> e.getAttend() != null).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(attendList)) {
                            attendNum = attendList.parallelStream().mapToInt(TenantStuGrowthCourseActivityStatistic::getAttend).sum();
                        }
                        // 获取应参与数
                        List<TenantStuGrowthCourseActivityStatistic> needList = tenantStuActivityStatisticList.parallelStream().filter(e -> e.getNeed() != null).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(needList)) {
                            needNum = needList.parallelStream().mapToInt(TenantStuGrowthCourseActivityStatistic::getNeed).sum();
                        }

                        TenantStuGrowthCourseActivityStatistic oldLEntity = tenantStuActivityStatisticList.get(0);
                        TenantStuGrowthCourseActivityStatistic tenantStuActivityStatistic = BeanHelper.copyObject(oldLEntity, TenantStuGrowthCourseActivityStatistic.class);
                        tenantStuActivityStatistic.setAttend(attendNum);
                        tenantStuActivityStatistic.setNeed(needNum);
                        return tenantStuActivityStatistic;
                    })
                    .collect(Collectors.toList());
            tenantStuGrowthCourseActivityStatisticDao.batchInsertOrUpdate(insertList);
        }
    }


}
