package com.ioie.kb.ms.provider;

import com.ioie.kb.ms.provider.res.BrainStromPvdRes;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
*
 * <p>
 * Description:头脑风暴远程获取接口
 * </p>
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2020-08-26 15:09:59
 * @see com.ioie.kb.ms.provider
 *
*/
public interface BrainStormProvide {

    String CONTEXT_PATH       = "/kb",
            BASE_PATH         = "/brainStorm-pvd/",
            LIST_BY_BRAINSTORMIDS_PATH = BASE_PATH + "list-by-brainStormIds",
            LIST_BY_BRAINSTORMID_PATH = BASE_PATH + "list-by-brainStormId";
    /**
     * 根据头脑风暴ids集合获取 头脑风暴列表
     *
     * @param ids 作业ids集合
     * @return {@link BrainStromPvdRes}
     */
    @Headers("Content-Type: application/json")
    @RequestLine("POST " + CONTEXT_PATH + LIST_BY_BRAINSTORMIDS_PATH)
    @PostMapping(LIST_BY_BRAINSTORMIDS_PATH)
    List<BrainStromPvdRes> listByBrainStormIds(List<Long> ids);

    /**
     * 根据头脑风暴id获取 头脑风暴基本信息
     *
     * @param id 作业id
     * @return {@link BrainStromPvdRes}
     */
    @Headers("Content-Type: application/json")
    @RequestLine("POST " + CONTEXT_PATH + LIST_BY_BRAINSTORMID_PATH)
    @PostMapping(LIST_BY_BRAINSTORMID_PATH)
    BrainStromPvdRes selectBrainStormById(Long id);

}
