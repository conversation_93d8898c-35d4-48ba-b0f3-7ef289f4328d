package com.ioie.job.log.model;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * description: 成长课 观看视频/课件按日统计
 *
 * <AUTHOR> wuyk
 * @version : 1.0
 * @since : 2022-05-11
 */
@Data
@Builder
public class TenantStuDayGrowthStatistic implements Serializable {

    /**
     * 会员id
     */
    private Long   memberId;
    /**
     * 事件日期
     */
    private Long   day;

    /**
     * 事件日期 yyyy-MM-dd
     */
    private String dayTxt;

    /**
     * 租户id
     */
    private Long   tenantId;

    /**
     * 观看视频时长，单位秒
     */
    private Integer duration;

    /**
     * 观看次数/课件的查看次数
     */
    private Integer   total;

    /**
     * video-观看视频;courseware-课程学习
     */
    private String type;

    /**
     * 成长路径id
     */
    private Long growthId;

    /**
     * 成长课id( 1-所有成长课   成长课id-当前成长课）
     */
    private Long courseId;
}
