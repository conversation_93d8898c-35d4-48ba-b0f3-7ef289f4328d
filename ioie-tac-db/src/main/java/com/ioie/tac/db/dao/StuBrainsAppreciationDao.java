package com.ioie.tac.db.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ioie.tac.db.entity.StuBrainsAppreciationEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: some desc
 * @menu: some name
 * @author: lld
 * @date: 2021/7/23 17:05
 */
public interface StuBrainsAppreciationDao extends BaseMapper<StuBrainsAppreciationEntity> {
    int updateBatch(List<StuBrainsAppreciationEntity> list);

    int updateBatchSelective(List<StuBrainsAppreciationEntity> list);

    int batchInsert(List<StuBrainsAppreciationEntity> list);

    int insertOrUpdate(StuBrainsAppreciationEntity record);

    int insertOrUpdateSelective(StuBrainsAppreciationEntity record);

    /**
     * 根据点赞成员id及学生头脑风暴ids获取列表
     * @param stuBrainsIds 学生头脑风暴ids
     * @param memberId 点赞成员id
     * @return {@link List<StuBrainsAppreciationEntity>}
     */
    List<StuBrainsAppreciationEntity> getListByStuBrainIdsAndMid(@Param("stuBrainsIds") List<Long> stuBrainsIds, @Param("memberId") Long memberId);

    /**
     * 根据点赞人及被点赞人及学生头脑风暴记录查询数据
     * @param stuStormId 生头脑风暴id
     * @param beAppreciatedMid 被点赞(被取消点赞)人成员id
     * @param memberId 点赞人
     * @return {@link List<StuBrainsAppreciationEntity>}
     */
    List<StuBrainsAppreciationEntity> getListByMemberIdAndStuBrainId(@Param("stuStormId") Long stuStormId, @Param("beAppreciatedMid") Long beAppreciatedMid, @Param("memberId") Long memberId);

    List<StuBrainsAppreciationEntity> getAvatar();

    int updateBatchImageUrl(List<StuBrainsAppreciationEntity> list);
}