package com.gok.pboot.service.entity.rem.dto;


import com.gok.pboot.service.entity.rem.domain.RemInsuranceProgramCustom;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 参保档案-办理增员-缴费金额dto
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AddPersonPayAmountDto  {

    /**
     * 企业、个人社保项自定义信息
     */
    private List<RemInsuranceProgramCustom> socialList;

    /**
     * 企业、个人公积金项自定义信息
     */
    private List<RemInsuranceProgramCustom> fundList;

    /**
     * 方案ID
     */
    private Long programId;
    /**
     * 参保标准 0公司统一标准 1自定义基数
     */
    private Integer insuranceStandard;
}
