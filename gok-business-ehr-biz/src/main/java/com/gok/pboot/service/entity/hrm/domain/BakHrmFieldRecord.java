package com.gok.pboot.service.entity.hrm.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.gok.pboot.service.commons.base.BeanEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 备份人事字段记录表
 *
 * <AUTHOR>
 * @date 2024/12/6
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="bak_hrm_field_record")
@Data
@Accessors(chain = true)
public class BakHrmFieldRecord extends BeanEntity<Long> implements Serializable {

    /**
     * 备份日期
     */
    @ApiModelProperty(name = "备份日期", notes = "")
    private LocalDate bakDate;
    /**
     * 主键id
     */
    @ApiModelProperty(name = "主键id", notes = "")
    private Long recordId;


    /**
     * 字段id（关联到人事字段表的id）
     */
    private Long fieldId;

    /**
     * 关联类型
     */
    private String relatedType;

    /**
     * 关联id
     */
    private Long relatedId;

    /**
     * 记录值
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String recordValue;



    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


}