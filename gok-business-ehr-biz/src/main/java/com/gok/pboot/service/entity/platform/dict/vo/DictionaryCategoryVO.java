package com.gok.pboot.service.entity.platform.dict.vo;

import com.gok.pboot.service.commons.base.BaseVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018年04月19日
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class DictionaryCategoryVO extends BaseVO<Long> {

    private static final long serialVersionUID = -6048119356756208036L;
    private String code;
    private String name;
    private String remark;
    private boolean removeAble;
    private boolean editAble;

}
