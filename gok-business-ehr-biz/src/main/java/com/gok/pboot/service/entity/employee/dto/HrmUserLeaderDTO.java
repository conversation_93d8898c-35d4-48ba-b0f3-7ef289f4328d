package com.gok.pboot.service.entity.employee.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * desc
 *
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @version : 1.0
 * @since : 2023-10-11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class HrmUserLeaderDTO {

    /**
     * 类型，默认1，直属上级
     */
    private Integer type;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 上级id
     */
    private Long userLeaderId;
    /**
     * 上级名字
     */
    private String userLeaderName;
}
