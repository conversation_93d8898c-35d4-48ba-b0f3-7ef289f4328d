package com.ioie.statistic.web.constant.enums;

/**
 * 从uc复制用户类型枚举
 * <AUTHOR>
 */
public enum TenantMemberTypeEnum {

    /**
     * 学生
     */
    STUDENT("学生", "student"),
    /**
     * 老师
     */
    TEACHER("老师", "teacher");

    private final String value;

    private final String name;

    TenantMemberTypeEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
