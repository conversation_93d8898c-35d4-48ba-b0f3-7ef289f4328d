package com.gok.components.track.resolver;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 用户信息
 *
 * <AUTHOR>
 * @date 2020年07月17日
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserInfo {

    /**
     * 用户id
     */
    private String uid;
    /**
     * token id
     */
    private String tid;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 昵称
     */
    private String nickname;

    /**
     * 微信昵称
     */
    private String wxName;
    /**
     * 头像
     */
    private String avatar;

    /**
     * 用户类型
     */
    private Integer type;

    /**
     * 超级管理员: 运管端或租户运营
     */
    private boolean superAdmin;

    /**
     * 是否为第一次登录 0: 否 1： 是
     */
    private Integer firstLogin;

    /**
     * 微信用户唯一标识
     */
    private String wxUnionid;

}
