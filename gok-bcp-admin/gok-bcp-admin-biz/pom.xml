<?xml version="1.0" encoding="UTF-8"?>

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.gok.bcp</groupId>
        <artifactId>gok-bcp-admin</artifactId>
        <version>1.1.333</version>
    </parent>

    <artifactId>gok-bcp-admin-biz</artifactId>
    <packaging>jar</packaging>
    <description>管理后台服务</description>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-module-file-biz</artifactId>
            <version>1.1.333</version>
        </dependency>
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-bcp-admin-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-excel</artifactId>
            <version>1.0.30</version>
        </dependency>
        <!-- mysql -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!--upms api、model 模块-->
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-bcp-upms-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-base-common-feign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-base-common-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-base-common-sentinel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-bcp-common-log</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                    <artifactId>spring-security-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.security.oauth</groupId>
                    <artifactId>spring-security-oauth2</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.gok.bcp</groupId>
            <artifactId>gok-base-common-gateway</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.gok.components</groupId>
            <artifactId>gok-components-file</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!--mybatis-->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <!-- druid 连接池 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <!--web 模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
        </dependency>

    </dependencies>

    <profiles>
        <!-- 开发环境 -->
        <profile>
            <id>dev</id>
            <properties>
                <gok-env>dev</gok-env>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <gok-env>test</gok-env>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <gok-env>prod</gok-env>
            </properties>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <includeSystemScope>true</includeSystemScope>
                    <mainClass>com.gok.AdminApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
